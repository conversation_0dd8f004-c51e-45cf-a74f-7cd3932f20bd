#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Binance自动交易系统
功能：
1. 行情监控
2. 智能市场状态判断（趋势/震荡/多变）
3. 基于多指标的交易信号生成
4. BTC限价单买入及卖出
5. 动态调整网格参数和风险管理
"""

print("脚本开始执行...")

import pandas as pd
import numpy as np
import time
import logging
import os
from datetime import datetime
import requests
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
import traceback

# 导入优化后的交易策略
from Trading_Strategy_Optimized import GridTradingStrategy

# ----------------- 配置加载 --------------------

# 加载.env文件中的环境变量
load_dotenv()

# API配置
API_KEY = os.getenv('API_KEY')
API_SECRET = os.getenv('API_SECRET')

# 交易配置
SYMBOL = os.getenv('SYMBOL', 'BTCUSDT')
TRADE_QTY = float(os.getenv('TRADE_QTY', '0.001'))

# 买入折扣百分比（例如90表示按现价的90%下单）
BUY_DISCOUNT_PERCENT = float(os.getenv('BUY_DISCOUNT_PERCENT', '100'))
if not (0 <= BUY_DISCOUNT_PERCENT <= 100):
    raise ValueError("BUY_DISCOUNT_PERCENT 必须在 0 到 100 之间")

# 订单超时时间（分钟）
ORDER_TIMEOUT_MINUTES = int(os.getenv('ORDER_TIMEOUT_MINUTES', '3'))

# 日志配置
LOG_FILE = 'trading_log.txt'

# 先清空日志文件
with open(LOG_FILE, 'w', encoding='utf-8') as f:
    f.write("")

# 配置日志
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8',  # 添加编码设置
)

# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

# 初始化Binance客户端（测试网）
try:
    client = Client(API_KEY, API_SECRET)
    client.API_URL = 'https://testnet.binance.vision/api'
    # 测试连接
    client.ping()
    print("成功连接到Binance API")
except Exception as e:
    print(f"连接Binance API失败: {e}")
    client = None

# ----------------- 辅助函数 --------------------

def log_info(message):
    """记录信息到日志文件和控制台"""
    try:
        # print(f"[INFO] {message}")
        logging.info(message)
    except Exception as e:
        print(f"日志记录错误: {e}")

def log_error(message):
    """记录错误到日志文件和控制台"""
    try:
        print(f"[ERROR] {message}")
        logging.error(message)
    except Exception as e:
        print(f"日志记录错误: {e}")

def get_current_price(symbol=SYMBOL):
    """获取当前价格"""
    try:
        ticker = client.get_symbol_ticker(symbol=symbol)
        return float(ticker['price'])
    except Exception as e:
        log_error(f"获取当前价格失败: {e}")
        return None

def get_klines(symbol=SYMBOL, interval='1m', limit=100):
    """获取K线数据"""
    try:
        klines = client.get_klines(symbol=symbol, interval=interval, limit=limit)

        # 转换为DataFrame
        df = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ])

        # 转换数据类型
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)

        return df
    except Exception as e:
        log_error(f"获取K线数据失败: {e}")
        return None

# ----------------- 策略辅助函数 --------------------

def prepare_data_for_strategy(klines_df):
    """
    将Binance K线数据转换为策略所需的格式

    参数:
        klines_df: DataFrame, 从Binance获取的K线数据

    返回:
        DataFrame: 转换后的数据
    """
    if klines_df is None or klines_df.empty:
        return None

    # 创建新的DataFrame
    df = pd.DataFrame()

    # 复制必要的列并重命名
    df['Open'] = klines_df['open']
    df['High'] = klines_df['high']
    df['Low'] = klines_df['low']
    df['Close'] = klines_df['close']
    df['Volume'] = klines_df['volume']

    # 设置索引
    df.index = klines_df['timestamp']

    return df

def analyze_market_with_strategy(df, grid_num=10, risk_pct=2.0):
    """
    使用优化后的策略分析市场

    参数:
        df: DataFrame, 包含OHLC数据
        grid_num: int, 网格数量
        risk_pct: float, 风险百分比

    返回:
        dict: 分析结果
    """
    if df is None or df.empty:
        return {
            'market_status': 'unknown',
            'signals': [],
            'grid_prices': [],
            'recommendation': 'HOLD'
        }

    # 创建策略实例
    strategy = GridTradingStrategy(df, grid_num=grid_num, risk_pct=risk_pct)

    # 判断市场状态
    market_status = strategy.judge_market_status()

    # 生成网格价格
    grid_prices = strategy.generate_grid_prices()

    # 生成交易信号
    signals = strategy.generate_signals()

    # 生成综合建议
    if signals:
        # 统计买入和卖出信号数量
        buy_signals = [s for s in signals if s[0] == 'buy']
        sell_signals = [s for s in signals if s[0] == 'sell']

        if len(buy_signals) > len(sell_signals):
            recommendation = 'BUY'
        elif len(sell_signals) > len(buy_signals):
            recommendation = 'SELL'
        else:
            recommendation = 'HOLD'
    else:
        recommendation = 'HOLD'

    return {
        'market_status': market_status,
        'signals': signals,
        'grid_prices': grid_prices,
        'recommendation': recommendation
    }

# ----------------- 交易功能 --------------------

def place_limit_order(symbol, side, quantity, price):
    """下限价单

    参数:
        symbol: 交易对
        side: 方向，'BUY' 或 'SELL'
        quantity: 数量
        price: 价格

    返回:
        order: 订单信息
    """
    # 检查是否在测试模式
    test_mode = os.getenv('TEST_MODE', 'true').lower() == 'true'

    if test_mode:
        # 测试模式，只记录订单信息而不实际下单
        log_info(f"[测试模式] 模拟下单: {side} {quantity} {symbol} @ {price}")
        order = {
            'symbol': symbol,
            'side': side,
            'type': 'LIMIT',
            'timeInForce': 'GTC',
            'quantity': quantity,
            'price': round(price, 2),
            'status': 'TEST',
            'orderId': int(time.time() * 1000)  # 使用时间戳作为订单ID
        }
        return order

    # 实际下单
    try:
        order = client.create_order(
            symbol=symbol,
            side=side,
            type='LIMIT',
            timeInForce='GTC',
            quantity=quantity,
            price=round(price, 2)
        )
        log_info(f"下单成功: {side} {quantity} {symbol} @ {price}")
        return order
    except BinanceAPIException as e:
        log_error(f"下单失败: {e}")
        return None
    except Exception as e:
        log_error(f"下单异常: {e}")
        return None

def cancel_order(symbol, order_id):
    """取消订单

    参数:
        symbol: 交易对
        order_id: 订单ID

    返回:
        result: 取消结果
    """
    # 检查是否在测试模式
    test_mode = os.getenv('TEST_MODE', 'true').lower() == 'true'

    if test_mode:
        # 测试模式，只记录取消订单信息
        log_info(f"[测试模式] 模拟取消订单: {order_id}")
        return {'orderId': order_id, 'status': 'CANCELED'}

    # 实际取消订单
    try:
        result = client.cancel_order(
            symbol=symbol,
            orderId=order_id
        )
        log_info(f"取消订单成功: {order_id}")
        return result
    except Exception as e:
        log_error(f"取消订单失败: {e}")
        return None

def check_order_status(symbol, order_id):
    """检查订单状态

    参数:
        symbol: 交易对
        order_id: 订单ID

    返回:
        status: 订单状态
    """
    # 检查是否在测试模式
    test_mode = os.getenv('TEST_MODE', 'true').lower() == 'true'

    if test_mode:
        # 测试模式，随机返回订单状态
        # 模拟80%的概率订单成交
        if time.time() % 10 < 8:  # 使用时间的个位数模拟随机
            return 'FILLED'
        else:
            return 'NEW'

    # 实际检查订单状态
    try:
        order = client.get_order(
            symbol=symbol,
            orderId=order_id
        )
        return order['status']
    except Exception as e:
        log_error(f"检查订单状态失败: {e}")
        return None

# ----------------- 主程序 --------------------

def main():
    """主程序"""
    log_info("程序启动，开始监控市场...")

    # 验证API密钥是否有效
    if not API_KEY or not API_SECRET or API_KEY == 'placeholder_api_key' or API_SECRET == 'placeholder_api_secret':
        log_error("API密钥未设置或是占位符，请在.env文件中设置有效的API密钥")
        return

    log_info(f"使用交易对: {SYMBOL}")
    log_info(f"买入折扣百分比: {BUY_DISCOUNT_PERCENT}%")
    log_info(f"每次交易数量: {TRADE_QTY}")

    # 检查客户端是否初始化成功
    if client is None:
        log_error("Binance客户端初始化失败，无法继续")
        return

    try:
        # 测试API连接
        log_info("测试API连接...")
        try:
            client.ping()
            log_info("API连接正常")
        except Exception as e:
            log_error(f"API连接失败: {e}")
            return

        # 获取当前价格
        current_price = get_current_price()
        if not current_price:
            log_error("无法获取当前价格，程序退出")
            return

        log_info(f"当前{SYMBOL}价格: {current_price}")

        # 获取不同时间周期的K线数据
        intervals = ['1h', '4h', '1d']  # 使用更长的时间周期进行分析
        analysis_results = {}

        for interval in intervals:
            # 获取K线数据
            klines_df = get_klines(interval=interval, limit=100)
            if klines_df is None or klines_df.empty:
                log_error(f"无法获取{interval}周期的K线数据")
                continue

            # 准备数据
            strategy_df = prepare_data_for_strategy(klines_df)
            if strategy_df is None:
                log_error(f"无法准备{interval}周期的策略数据")
                continue

            # 使用优化后的策略分析市场
            result = analyze_market_with_strategy(strategy_df)
            analysis_results[interval] = result

            # 不输出市场状态信息
            # 市场状态: {result['market_status']}

            # 记录信号详情 - 不再单独打印每个信号

        # 综合分析不同时间周期的结果
        if not analysis_results:
            log_error("没有足够的分析结果，无法生成交易信号")
            return

        # 统计各时间周期的建议
        buy_count = sum(1 for result in analysis_results.values() if result['recommendation'] == 'BUY')
        sell_count = sum(1 for result in analysis_results.values() if result['recommendation'] == 'SELL')

        # 生成综合建议
        if buy_count > sell_count and buy_count >= len(analysis_results) / 2:
            # 买入信号
            log_info("综合分析: 买入信号")

            # 获取最佳买入价格和止损点
            best_buy_signal = None
            for result in analysis_results.values():
                for signal in result['signals']:
                    if signal[0] == 'buy':
                        if best_buy_signal is None or signal[1] > best_buy_signal[1]:
                            best_buy_signal = signal

            if best_buy_signal:
                action = best_buy_signal[0]
                price = best_buy_signal[1]
                stop_loss = best_buy_signal[3]

                # 应用折扣
                buy_price = price * (BUY_DISCOUNT_PERCENT / 100)

                log_info(f"最佳买入价格: {buy_price:.2f}, 止损点: {stop_loss:.2f}")

                # 下买入限价单
                order = place_limit_order(SYMBOL, 'BUY', TRADE_QTY, buy_price)

                if order:
                    log_info(f"买入订单已提交: {order['orderId']}")

                    # 监控订单状态
                    start_time = time.time()
                    while time.time() - start_time < ORDER_TIMEOUT_MINUTES * 60:
                        status = check_order_status(SYMBOL, order['orderId'])

                        if status == 'FILLED':
                            log_info(f"买入订单已成交")
                            break
                        elif status == 'CANCELED' or status == 'REJECTED' or status == 'EXPIRED':
                            log_info(f"买入订单未成交: {status}")
                            break

                        time.sleep(10)  # 每10秒检查一次

                    # 超时取消订单
                    if time.time() - start_time >= ORDER_TIMEOUT_MINUTES * 60:
                        log_info(f"买入订单超时，准备取消")
                        cancel_order(SYMBOL, order['orderId'])
            else:
                log_info("没有找到合适的买入信号")

        elif sell_count > buy_count and sell_count >= len(analysis_results) / 2:
            # 卖出信号
            log_info("综合分析: 卖出信号")

            # 获取最佳卖出价格和止损点
            best_sell_signal = None
            for result in analysis_results.values():
                for signal in result['signals']:
                    if signal[0] == 'sell':
                        if best_sell_signal is None or signal[1] < best_sell_signal[1]:
                            best_sell_signal = signal

            if best_sell_signal:
                action = best_sell_signal[0]
                price = best_sell_signal[1]
                stop_loss = best_sell_signal[3]

                # 卖出价格略低于信号价格
                sell_price = price * 0.999

                log_info(f"最佳卖出价格: {sell_price:.2f}, 止损点: {stop_loss:.2f}")

                # 下卖出限价单
                order = place_limit_order(SYMBOL, 'SELL', TRADE_QTY, sell_price)

                if order:
                    log_info(f"卖出订单已提交: {order['orderId']}")

                    # 监控订单状态
                    start_time = time.time()
                    while time.time() - start_time < ORDER_TIMEOUT_MINUTES * 60:
                        status = check_order_status(SYMBOL, order['orderId'])

                        if status == 'FILLED':
                            log_info(f"卖出订单已成交")
                            break
                        elif status == 'CANCELED' or status == 'REJECTED' or status == 'EXPIRED':
                            log_info(f"卖出订单未成交: {status}")
                            break

                        time.sleep(10)  # 每10秒检查一次

                    # 超时取消订单
                    if time.time() - start_time >= ORDER_TIMEOUT_MINUTES * 60:
                        log_info(f"卖出订单超时，准备取消")
                        cancel_order(SYMBOL, order['orderId'])
            else:
                log_info("没有找到合适的卖出信号")

        else:
            # 观望信号
            log_info("综合分析: 观望信号，不进行交易")

            # 不输出市场状态分析
            # market_states = [f"{interval}:{result['market_status']}" for interval, result in analysis_results.items()]
            # log_info(f"市场状态分析: {', '.join(market_states)}")

    except Exception as e:
        error_msg = f"程序执行异常: {e}"
        log_error(error_msg)
        # 记录详细的错误堆栈
        error_traceback = traceback.format_exc()
        log_error(f"错误详情:\n{error_traceback}")

if __name__ == "__main__":
    print("程序开始执行...")

    # 记录启动信息到日志文件
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()} - 程序开始执行...\n")

    # 设置最大运行次数
    max_runs = 50
    run_count = 0

    while run_count < max_runs:
        try:
            print(f"执行第 {run_count + 1} 次...")

            # 记录到日志文件
            with open(LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now()} - 执行第 {run_count + 1} 次...\n")

            main()
            log_info("等待下一轮检测...")

            # 增加运行次数
            run_count += 1

            # 如果还没达到最大次数，等待一段时间
            if run_count < max_runs:
                print(f"等待 10 秒后进行下一轮...")
                time.sleep(10)  # 每10秒执行一次
        except KeyboardInterrupt:
            print("程序被用户中断")
            log_info("程序被用户中断")
            break
        except Exception as e:
            error_msg = f"主循环异常: {e}"
            print(error_msg)
            log_error(error_msg)

            # 记录详细的错误堆栈
            error_traceback = traceback.format_exc()
            log_error(f"错误详情:\n{error_traceback}")

            time.sleep(10)  # 发生异常后等待10秒再继续

    print("程序执行完毕，达到最大运行次数。")
