import requests
import time

# 简单测试币安API连接
print("开始测试币安API连接...")

try:
    # 测试公共API端点（不需要签名）
    url = "https://testnet.binance.vision/api/v3/ping"
    print(f"正在请求: {url}")
    
    response = requests.get(url, timeout=10)
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 获取服务器时间
    time_url = "https://testnet.binance.vision/api/v3/time"
    print(f"\n正在请求服务器时间: {time_url}")
    
    time_response = requests.get(time_url, timeout=10)
    print(f"响应状态码: {time_response.status_code}")
    print(f"响应内容: {time_response.text}")
    
    # 获取交易对信息
    symbols_url = "https://testnet.binance.vision/api/v3/ticker/price?symbol=BTCUSDT"
    print(f"\n正在获取BTCUSDT价格: {symbols_url}")
    
    symbols_response = requests.get(symbols_url, timeout=10)
    print(f"响应状态码: {symbols_response.status_code}")
    print(f"响应内容: {symbols_response.text}")
    
except Exception as e:
    print(f"测试过程中出错: {e}")

print("\n测试完成")
