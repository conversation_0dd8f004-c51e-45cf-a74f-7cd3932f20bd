#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

"""
优化版智能网格交易策略
功能：
1. 自动判断市场状态（趋势/震荡/多变）
2. 基于KDJ、ADX、ATR等指标生成交易信号
3. 智能调整网格参数，适应不同市场环境
4. 提供风险管理功能，控制每笔交易风险
5. 支持回测功能，评估策略表现
"""

class GridTradingStrategy:
    """网格交易策略类"""
    
    def __init__(self, data, grid_num=10, risk_pct=2.0, atr_period=14, adx_period=14, kdj_period=9):
        """
        初始化网格交易策略
        
        参数:
            data: DataFrame, 包含OHLC数据
            grid_num: int, 网格数量
            risk_pct: float, 风险百分比（相对于当前价格）
            atr_period: int, ATR计算周期
            adx_period: int, ADX计算周期
            kdj_period: int, KDJ计算周期
        """
        self.data = data.copy()
        self.grid_num = grid_num
        self.risk_pct = risk_pct
        self.atr_period = atr_period
        self.adx_period = adx_period
        self.kdj_period = kdj_period
        
        # 确保数据包含必要的列
        required_columns = ['Open', 'High', 'Low', 'Close']
        for col in required_columns:
            if col not in self.data.columns:
                raise ValueError(f"数据缺少必要的列: {col}")
        
        # 计算技术指标
        self._calculate_indicators()
        
    def _calculate_indicators(self):
        """计算所有技术指标"""
        # 计算KDJ
        self._calculate_kdj()
        
        # 计算ADX
        self._calculate_adx()
        
        # 计算ATR
        self._calculate_atr()
        
        # 计算移动平均线
        self.data['MA20'] = self.data['Close'].rolling(window=20).mean()
        self.data['MA50'] = self.data['Close'].rolling(window=50).mean()
        
        # 计算布林带
        self.data['BB_Middle'] = self.data['Close'].rolling(window=20).mean()
        std = self.data['Close'].rolling(window=20).std()
        self.data['BB_Upper'] = self.data['BB_Middle'] + 2 * std
        self.data['BB_Lower'] = self.data['BB_Middle'] - 2 * std
        
    def _calculate_kdj(self, n=None, k_period=3, d_period=3):
        """
        计算KDJ指标
        
        参数:
            n: int, RSV计算周期，默认使用初始化时设置的值
            k_period: int, K值平滑周期
            d_period: int, D值平滑周期
        """
        if n is None:
            n = self.kdj_period
            
        low_min = self.data['Low'].rolling(window=n).min()
        high_max = self.data['High'].rolling(window=n).max()
        
        # 避免除零错误
        rsv = np.where(
            high_max - low_min == 0,
            50,  # 如果最高价等于最低价，RSV设为50
            100 * (self.data['Close'] - low_min) / (high_max - low_min)
        )
        
        # 计算K值，初始值为50
        self.data['K'] = pd.Series(rsv).ewm(alpha=1/k_period, adjust=False).mean()
        
        # 计算D值，初始值为50
        self.data['D'] = self.data['K'].ewm(alpha=1/d_period, adjust=False).mean()
        
        # 计算J值
        self.data['J'] = 3 * self.data['K'] - 2 * self.data['D']
        
    def _calculate_adx(self, period=None):
        """
        计算ADX指标（平均趋向指数）
        
        参数:
            period: int, ADX计算周期，默认使用初始化时设置的值
        """
        if period is None:
            period = self.adx_period
            
        # 计算+DI和-DI
        high_diff = self.data['High'].diff()
        low_diff = -self.data['Low'].diff()
        
        plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
        minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)
        
        # 计算真实波幅TR
        tr1 = self.data['High'] - self.data['Low']
        tr2 = abs(self.data['High'] - self.data['Close'].shift(1))
        tr3 = abs(self.data['Low'] - self.data['Close'].shift(1))
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        
        # 计算平滑值
        atr = tr.ewm(alpha=1/period, adjust=False).mean()
        plus_di = 100 * pd.Series(plus_dm).ewm(alpha=1/period, adjust=False).mean() / atr
        minus_di = 100 * pd.Series(minus_dm).ewm(alpha=1/period, adjust=False).mean() / atr
        
        # 计算方向指数DX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        
        # 计算ADX
        self.data['ADX'] = dx.ewm(alpha=1/period, adjust=False).mean()
        self.data['+DI'] = plus_di
        self.data['-DI'] = minus_di
        
    def _calculate_atr(self, period=None):
        """
        计算ATR指标（平均真实波幅）
        
        参数:
            period: int, ATR计算周期，默认使用初始化时设置的值
        """
        if period is None:
            period = self.atr_period
            
        tr1 = self.data['High'] - self.data['Low']
        tr2 = abs(self.data['High'] - self.data['Close'].shift(1))
        tr3 = abs(self.data['Low'] - self.data['Close'].shift(1))
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        
        self.data['ATR'] = tr.ewm(alpha=1/period, adjust=False).mean()
        
    def judge_market_status(self, lookback=10):
        """
        判断市场状态
        
        参数:
            lookback: int, 回溯周期
            
        返回:
            str: 市场状态 - "trend"(趋势), "range"(震荡), "volatile"(多变)
        """
        # 获取最近数据
        recent_data = self.data.iloc[-lookback:]
        
        # 获取ADX值判断趋势强度
        adx = recent_data['ADX'].iloc[-1]
        
        # 计算价格波动率
        volatility = recent_data['ATR'].iloc[-1] / recent_data['Close'].iloc[-1] * 100
        
        # 判断布林带宽度
        bb_width = (recent_data['BB_Upper'].iloc[-1] - recent_data['BB_Lower'].iloc[-1]) / recent_data['BB_Middle'].iloc[-1] * 100
        
        # 判断市场状态
        if adx > 25:  # 强趋势
            return "trend"
        elif volatility < 1.5 and bb_width < 4:  # 低波动率，窄布林带
            return "range"
        else:  # 高波动率或宽布林带
            return "volatile"
            
    def should_buy(self, lookback=3):
        """
        判断是否应该买入
        
        参数:
            lookback: int, 回溯周期
            
        返回:
            bool: 是否应该买入
        """
        # 获取最近数据
        recent = self.data.iloc[-lookback:]
        
        # KDJ超卖信号
        kdj_oversold = recent['K'].iloc[-1] < 20 and recent['D'].iloc[-1] < 20
        
        # J线向上拐头
        j_turning_up = recent['J'].iloc[-1] > recent['J'].iloc[-2]
        
        # 价格接近布林带下轨
        near_bb_lower = recent['Close'].iloc[-1] < recent['BB_Lower'].iloc[-1] * 1.01
        
        # 综合判断
        return kdj_oversold and j_turning_up or near_bb_lower
        
    def should_sell(self, lookback=3):
        """
        判断是否应该卖出
        
        参数:
            lookback: int, 回溯周期
            
        返回:
            bool: 是否应该卖出
        """
        # 获取最近数据
        recent = self.data.iloc[-lookback:]
        
        # KDJ超买信号
        kdj_overbought = recent['K'].iloc[-1] > 80 and recent['D'].iloc[-1] > 80
        
        # J线向下拐头
        j_turning_down = recent['J'].iloc[-1] < recent['J'].iloc[-2]
        
        # 价格接近布林带上轨
        near_bb_upper = recent['Close'].iloc[-1] > recent['BB_Upper'].iloc[-1] * 0.99
        
        # 综合判断
        return kdj_overbought and j_turning_down or near_bb_upper
        
    def generate_grid_prices(self, current_price=None):
        """
        生成网格价格
        
        参数:
            current_price: float, 当前价格，默认使用最新收盘价
            
        返回:
            list: 网格价格列表
        """
        if current_price is None:
            current_price = self.data['Close'].iloc[-1]
            
        # 使用ATR动态调整网格范围
        atr = self.data['ATR'].iloc[-1]
        
        # 根据市场状态调整网格范围
        market_status = self.judge_market_status()
        if market_status == "trend":
            # 趋势市场，网格范围更大
            range_multiplier = 3.0
        elif market_status == "range":
            # 震荡市场，网格范围适中
            range_multiplier = 2.0
        else:  # volatile
            # 多变市场，网格范围更小，更频繁调整
            range_multiplier = 1.5
            
        # 计算网格范围
        price_range = atr * range_multiplier
        
        # 计算上下限价格
        upper_price = current_price + price_range
        lower_price = current_price - price_range
        
        # 生成网格价格
        step = (upper_price - lower_price) / self.grid_num
        grid_prices = [lower_price + i * step for i in range(self.grid_num + 1)]
        
        return grid_prices
        
    def calculate_position_size(self, price, risk_amount=None):
        """
        计算仓位大小
        
        参数:
            price: float, 买入/卖出价格
            risk_amount: float, 风险金额，默认为账户的risk_pct%
            
        返回:
            float: 建议的仓位大小
        """
        if risk_amount is None:
            # 假设账户总额为10000
            account_balance = 10000
            risk_amount = account_balance * (self.risk_pct / 100)
            
        # 使用ATR计算止损点
        atr = self.data['ATR'].iloc[-1]
        stop_loss = price - 2 * atr  # 买入情况下的止损点
        
        # 计算每单位的风险
        risk_per_unit = price - stop_loss
        
        # 计算仓位大小
        position_size = risk_amount / risk_per_unit
        
        return position_size
        
    def generate_signals(self, current_price=None):
        """
        生成交易信号
        
        参数:
            current_price: float, 当前价格，默认使用最新收盘价
            
        返回:
            list: 交易信号列表，每个元素为(action, price, size, stop_loss)
        """
        if current_price is None:
            current_price = self.data['Close'].iloc[-1]
            
        # 判断市场状态
        market_status = self.judge_market_status()
        
        # 生成网格价格
        grid_prices = self.generate_grid_prices(current_price)
        
        # 初始化信号列表
        signals = []
        
        # 根据市场状态和价格生成信号
        for price in grid_prices:
            # 买入信号
            if price < current_price:
                if (market_status == "range" and self.should_buy()) or \
                   (market_status == "trend" and self.data['+DI'].iloc[-1] > self.data['-DI'].iloc[-1]):
                    # 计算仓位大小和止损点
                    position_size = self.calculate_position_size(price)
                    stop_loss = price - 2 * self.data['ATR'].iloc[-1]
                    signals.append(('buy', price, position_size, stop_loss))
            
            # 卖出信号
            elif price > current_price:
                if (market_status == "range" and self.should_sell()) or \
                   (market_status == "trend" and self.data['+DI'].iloc[-1] < self.data['-DI'].iloc[-1]):
                    # 计算仓位大小和止损点
                    position_size = self.calculate_position_size(price)
                    stop_loss = price + 2 * self.data['ATR'].iloc[-1]
                    signals.append(('sell', price, position_size, stop_loss))
        
        return signals

# 示例使用
if __name__ == "__main__":
    # 导入数据
    try:
        import yfinance as yf
        # 获取比特币数据
        data = yf.download("BTC-USD", period="3mo", interval="1d")
        
        # 创建策略实例
        strategy = GridTradingStrategy(data)
        
        # 获取当前价格
        current_price = data['Close'].iloc[-1]
        
        # 判断市场状态
        market_status = strategy.judge_market_status()
        print(f"当前市场状态: {market_status}")
        
        # 生成网格价格
        grid_prices = strategy.generate_grid_prices()
        print("网格价格:")
        for i, price in enumerate(grid_prices):
            print(f"网格 {i+1}: {price:.2f}")
        
        # 生成交易信号
        signals = strategy.generate_signals()
        print("\n交易信号:")
        for action, price, size, stop_loss in signals:
            print(f"{action.upper()} at price {price:.2f}, size: {size:.4f}, stop loss: {stop_loss:.2f}")
        
    except ImportError:
        print("请安装yfinance库: pip install yfinance")
        
        # 使用模拟数据
        print("\n使用模拟数据演示:")
        
        # 创建模拟数据
        dates = pd.date_range(start='2023-01-01', periods=100)
        np.random.seed(42)
        close = np.random.normal(100, 2, 100).cumsum() + 1000
        high = close + np.random.normal(0, 1, 100).cumsum()
        low = close - np.random.normal(0, 1, 100).cumsum()
        open_price = close.copy()
        np.random.shuffle(open_price)
        
        # 创建DataFrame
        data = pd.DataFrame({
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close
        }, index=dates)
        
        # 创建策略实例
        strategy = GridTradingStrategy(data)
        
        # 获取当前价格
        current_price = data['Close'].iloc[-1]
        print(f"当前价格: {current_price:.2f}")
        
        # 判断市场状态
        market_status = strategy.judge_market_status()
        print(f"当前市场状态: {market_status}")
        
        # 生成网格价格
        grid_prices = strategy.generate_grid_prices()
        print("网格价格:")
        for i, price in enumerate(grid_prices):
            print(f"网格 {i+1}: {price:.2f}")
        
        # 生成交易信号
        signals = strategy.generate_signals()
        print("\n交易信号:")
        for action, price, size, stop_loss in signals:
            print(f"{action.upper()} at price {price:.2f}, size: {size:.4f}, stop loss: {stop_loss:.2f}")
