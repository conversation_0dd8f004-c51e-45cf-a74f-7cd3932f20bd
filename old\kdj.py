from binance.client import Client
import pandas as pd
import ta  # 用来计算KDJ等指标
import time
import os

# 一、金叉（买入信号）： 当 K 值从下向上突破 D 值，且两者均处于 ** 超卖区（K<20、D<20）** 时，
# 视为短期趋势反转的买入信号。 ◦ 有效性验证：若金叉伴随成交量放大或价格突破关键阻力位，信号可靠性更高。
# 二死叉（卖出信号）：  当 K 值从上向下跌破 D 值，且两者均处于 ** 超买区（K>80、D>80）** 时，
# 视为短期趋势反转的卖出信号。 ◦ 风险提示：死叉在下跌趋势中可能提前反映抛售压力，需结合市场情绪判断。

# •  超买信号： ◦ K/D 值 > 80 且 J 值 > 100，表明市场短期上涨过度，可能回调，可考虑减仓或止盈。 
#   注意：在强势单边行情中（如牛市），超买信号可能延迟，需结合价格走势判断。   
# •  超卖信号： ◦ K/D 值 < 20 且 J 值 < 0，表明市场短期下跌过度，可能反弹，可关注反弹机会或分批建仓。 
#   注意：在弱势单边行情中（如熊市），超卖信号可能失效，需警惕持续下跌风险。



# 初始化 Binance API
api_key = 'MZcSaSSnxF6gb2WZ67YGNg2ssiGsjoV41X8p2IfHvPofSaNBrTsizS5B5wy8US8U'
api_secret = '4vczP44px2GEO4tXSIxPLJqHAr5VnNB8dtmhkLRbIGjHWVuOHGDBhKhtUZrVCnpX'
client = Client(api_key, api_secret)

# 创建CSV文件存储结果
csv_filename = 'btcusdt_kdj_data.csv'
csv_exists = os.path.isfile(csv_filename)

for i in range(60):
    time.sleep(2)
    # 获取K线数据（例如15分钟线）
    klines_1m = client.get_klines(symbol='BTCUSDT', interval=Client.KLINE_INTERVAL_3MINUTE, limit=100)

    # 转成DataFrame
    df = pd.DataFrame(klines_1m, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
    ])

    # 转换数据类型
    df['open'] = df['open'].astype(float)
    df['high'] = df['high'].astype(float)
    df['low'] = df['low'].astype(float)
    df['close'] = df['close'].astype(float)

    # 计算 KDJ（使用 ta 库中的 stoch 指标）
    stoch = ta.momentum.StochasticOscillator(
        high=df['high'],
        low=df['low'],
        close=df['close'],
        window=5,               # 周期 N=5，K/D/J 的移动平均周期 = 3，适用场景：小时线或 15 分钟线分析，适合高频交易；N=9，日线级别分析，捕捉中期波段机会。
        smooth_window=3
    )

    df['K'] = stoch.stoch()
    df['D'] = stoch.stoch_signal()
    df['J'] = 3 * df['K'] - 2 * df['D']

    # 查看最后几行
    print(df[['close', 'K', 'D', 'J']].tail(1))
    
    # 保存最新记录到CSV文件
    result_df = df[['timestamp', 'close', 'K', 'D', 'J']].tail(1)

result_df['datetime'] = pd.to_datetime(result_df['timestamp'], unit='ms')
    
# 如果文件不存在，创建新文件并写入表头，否则追加数据
result_df.to_csv(csv_filename, mode='a', header=not csv_exists, index=False)
if not csv_exists:
    csv_exists = True  # 第一次写入后，后续都是追加模式