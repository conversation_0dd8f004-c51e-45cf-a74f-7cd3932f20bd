#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的WebSocket连接测试
"""

import websocket
import json
import threading
import time
from datetime import datetime

def on_message(ws, message):
    """处理WebSocket消息"""
    try:
        data = json.loads(message)
        print(f"{datetime.now().strftime('%H:%M:%S')} - 收到消息: {data}")
        
        # 检查是否是K线数据
        if 'k' in data:
            kline = data['k']
            print(f"K线数据: 价格={kline['c']}, 成交量={kline['v']}, 完成={kline['x']}")
        
        # 检查是否是价格数据
        if 'c' in data:
            print(f"价格数据: {data['c']}")
            
    except Exception as e:
        print(f"处理消息失败: {e}")
        print(f"原始消息: {message}")

def on_error(ws, error):
    """WebSocket错误处理"""
    print(f"WebSocket错误: {error}")

def on_close(ws, close_status_code, close_msg):
    """WebSocket关闭处理"""
    print("WebSocket连接已关闭")

def on_open(ws):
    """WebSocket连接打开"""
    print("WebSocket连接已建立")

def test_single_stream():
    """测试单一流连接"""
    # 测试单一K线流
    stream_url = "wss://stream.binance.com:9443/ws/arbusdt@kline_1m"
    
    print(f"连接URL: {stream_url}")
    
    # 创建WebSocket连接
    ws = websocket.WebSocketApp(stream_url,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close,
                                on_open=on_open)
    
    # 在单独线程中运行
    def run_ws():
        ws.run_forever()
    
    ws_thread = threading.Thread(target=run_ws, daemon=True)
    ws_thread.start()
    
    return ws

def test_ticker_stream():
    """测试价格流连接"""
    # 测试价格流
    stream_url = "wss://stream.binance.com:9443/ws/arbusdt@ticker"
    
    print(f"连接URL: {stream_url}")
    
    # 创建WebSocket连接
    ws = websocket.WebSocketApp(stream_url,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close,
                                on_open=on_open)
    
    # 在单独线程中运行
    def run_ws():
        ws.run_forever()
    
    ws_thread = threading.Thread(target=run_ws, daemon=True)
    ws_thread.start()
    
    return ws

if __name__ == "__main__":
    print("=" * 60)
    print("简单WebSocket连接测试")
    print("=" * 60)
    
    try:
        print("\n1. 测试K线流...")
        ws1 = test_single_stream()
        time.sleep(15)
        
        print("\n2. 测试价格流...")
        ws2 = test_ticker_stream()
        time.sleep(15)
        
        print("\n测试完成")
        
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")
