# import socket

# host_name = socket.gethostname()
# host_ips = socket.gethostbyname_ex(host_name)

# print("主机名:", host_name)
# print("所有本地IP地址:", host_ips[2])

# # ************ ************* *********** **********

  # curl https://api.ipify.org


# import pygetwindow as gw

# for w in gw.getAllTitles():
#   print()
#   print(w)

import datetime

timestamp = 1748524457489 / 1000  # 毫秒转秒
print(datetime.datetime.fromtimestamp(timestamp))

