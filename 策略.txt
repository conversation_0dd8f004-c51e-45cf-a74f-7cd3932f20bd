H1:=MAX(DYNAINFO(3),DYNAINFO(5));

L1:=MIN(DYNAINFO(3),DYNAINFO(6));

P1:=H1-L1;

阻力:=L1+P1*7/8,COLORGREEN;

支撑:=L1+P1*0.5/8,COLORRED;

中线:=(支撑+阻力)/2,COLORWHITE,POINTDOT;

V11:=3*SMA((C-LLV(L,55))/(HHV(H,55)-LLV(L,55))*100,5,1)-2*SMA(SMA((C-LLV(L,55))/(HHV(H,55)-LLV(L,55))*100,5,1),3,1);

趋势线:EMA(V11,3),LINETHICK1,COLORYELLOW;

V12:=(趋势线-REF(趋势线,1))/REF(趋势线,1)*100;

准备买入:STICKLINE(趋势线<11,趋势线,11,5,0),COLORRED;

AA:=(趋势线<11) AND FILTER((趋势线<=11),15) AND C<中线;

BB0:=REF(趋势线,1)<11 AND CROSS(趋势线,11) AND C<中线;

BB1:=REF(趋势线,1)<11 AND REF(趋势线,1)>6 AND CROSS(趋势线,11);

BB2:=REF(趋势线,1)<6 AND REF(趋势线,1)>3 AND CROSS(趋势线,6);


BB3:=REF(趋势线,1)<3 AND REF(趋势线,1)>1 AND CROSS(趋势线,3);


BB4:=REF(趋势线,1)<1 AND REF(趋势线,1)>0 AND CROSS(趋势线,1);

BB5:=REF(趋势线,1)<0 AND CROSS(趋势线,0);

BB:=BB1=1 OR BB2=1 OR BB3=1 OR BB4=1 OR BB5=1;

下单买入:STICKLINE(BB=1 AND C<中线,11,52,1,0),COLORRED;

DRAWICON(BB=1 AND C<中线,55,1);

DRAWTEXT(BB0,60,'★抄底'),COLORRED;

DRAWTEXT(AA,16,'超卖见底'),,COLORWHITE;

准备卖出:STICKLINE(趋势线>89,趋势线,89,5,0),COLORGREEN;

CC:=(趋势线>89) AND FILTER((趋势线>89),15) AND C>中线;

DD0:=REF(趋势线,1)>89 AND CROSS(89,趋势线) AND C>中线;

DD1:=REF(趋势线,1)>89 AND REF(趋势线,1)<94 AND CROSS(89,趋势线);

DD2:=REF(趋势线,1)>94 AND REF(趋势线,1)<97 AND CROSS(94,趋势线);

DD3:=REF(趋势线,1)>97 AND REF(趋势线,1)>99 AND CROSS(97,趋势线);


DD4:=REF(趋势线,1)>99 AND REF(趋势线,1)<100 AND CROSS(99,趋势线);

DD5:=REF(趋势线,1)>100 AND CROSS(100,趋势线);

DD:=DD1=1 OR DD2=1 OR DD3=1 OR DD4=1 OR DD5=1;

下单卖出:STICKLINE(DD=1 AND C>中线,89,49,1,0),COLORGREEN;

DRAWICON(DD=1 AND C>中线,55,2);

DRAWTEXT(DD0,40,'★逃顶'),COLORGREEN;

DRAWTEXT (CC,84,'超买见顶'),,COLORWHITE;

顶:89,COLORGREEN;

底:11,COLORRED;

中:50,POINTDOT,COLORWHITE;

DRAWTEXT(ISLASTBAR,顶,'顶'),COLORGREEN;

DRAWTEXT(ISLASTBAR,底,'底'),COLORRED;

DRAWTEXT(ISLASTBAR,中,'中'),COLORWHITE;