# 智能网格交易策略

基于KDJ、ADX、ATR等技术指标的智能网格交易策略，支持市场状态自动判断、动态调整网格参数、风险管理和回测功能。

## 功能特点

- **市场状态自动判断**：根据ADX、ATR和布林带宽度自动判断市场是处于趋势、震荡还是多变状态
- **多指标交易信号**：结合KDJ、布林带、方向指标等生成交易信号
- **动态网格参数**：根据市场状态和波动率自动调整网格范围和数量
- **风险管理**：基于ATR计算止损位置和仓位大小，控制每笔交易风险
- **回测功能**：支持历史数据回测，评估策略性能
- **参数优化**：支持网格参数优化，寻找最优参数组合
- **Binance API集成**：支持连接Binance API进行实时交易

## 文件说明

- `Trading_Strategy_Optimized.py`：优化版网格交易策略核心类
- `backtest.py`：回测模块，用于评估策略性能
- `run_strategy.py`：运行策略的主程序，支持回测和实时交易模式
- `GRID_STRATEGY_README.md`：项目说明文档

## 安装依赖

```bash
pip install pandas numpy matplotlib python-binance python-dotenv yfinance
```

## 使用方法

### 1. 配置环境变量

创建`.env`文件，包含以下内容：

```
API_KEY=你的Binance API Key
API_SECRET=你的Binance API Secret
```

### 2. 回测模式

运行回测，评估策略性能：

```bash
python run_strategy.py --symbol BTCUSDT --interval 1d --grid_num 10 --risk_pct 2.0 --backtest
```

参数说明：
- `--symbol`：交易对，默认为BTCUSDT
- `--interval`：时间间隔，默认为1d（日线）
- `--grid_num`：网格数量，默认为10
- `--risk_pct`：风险百分比，默认为2.0%
- `--backtest`：启用回测模式

### 3. 实时交易模式

运行实时交易策略：

```bash
python run_strategy.py --symbol BTCUSDT --interval 1d --grid_num 10 --risk_pct 2.0
```

不加`--backtest`参数即为实时交易模式。

### 4. 直接使用策略类

也可以在自己的代码中直接使用策略类：

```python
from Trading_Strategy_Optimized import GridTradingStrategy
import pandas as pd

# 加载数据
data = pd.read_csv('your_data.csv')
data.set_index('date', inplace=True)

# 创建策略实例
strategy = GridTradingStrategy(data, grid_num=10, risk_pct=2.0)

# 判断市场状态
market_status = strategy.judge_market_status()
print(f"当前市场状态: {market_status}")

# 生成网格价格
grid_prices = strategy.generate_grid_prices()

# 生成交易信号
signals = strategy.generate_signals()
for action, price, size, stop_loss in signals:
    print(f"{action.upper()} at price {price:.2f}, size: {size:.4f}, stop loss: {stop_loss:.2f}")
```

## 策略原理

### 市场状态判断

- **趋势市场**：ADX > 25，表示存在明显趋势
- **震荡市场**：波动率低且布林带窄，适合网格交易
- **多变市场**：波动率高或布林带宽，需要更频繁调整网格

### 交易信号生成

- **买入信号**：
  - 震荡市场：KDJ超卖且J线向上拐头，或价格接近布林带下轨
  - 趋势市场：+DI > -DI，表示上升趋势

- **卖出信号**：
  - 震荡市场：KDJ超买且J线向下拐头，或价格接近布林带上轨
  - 趋势市场：+DI < -DI，表示下降趋势

### 网格参数动态调整

- 根据ATR动态计算网格范围
- 根据市场状态调整范围乘数：
  - 趋势市场：范围更大
  - 震荡市场：范围适中
  - 多变市场：范围更小，更频繁调整

### 风险管理

- 使用ATR计算止损点
- 根据账户风险承受能力和止损点计算仓位大小
- 控制每笔交易的风险百分比

## 回测指标

- **总收益率**：策略在回测期间的总收益率
- **年化收益率**：年化后的收益率
- **最大回撤**：最大的资金回撤百分比
- **夏普比率**：收益与风险的比率
- **交易次数**：买入和卖出的总次数

## 注意事项

- 本策略仅供学习和研究使用，不构成投资建议
- 实盘交易前请充分测试策略性能
- 请确保API密钥安全，建议只开启交易权限，不开启提现权限
- 交易有风险，投资需谨慎

## 未来改进

- 添加更多技术指标和交易信号
- 支持更多交易所API
- 添加止盈止损功能
- 开发图形用户界面
- 添加实时监控和通知功能

## 许可证

MIT
