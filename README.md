# Binance自动交易系统

基于Binance API的自动交易系统，支持行情监控、KDJ和网格分析、BTC限价单买入及卖出。

## 功能特点

- **行情监控**：实时监控BTC价格变动
- **技术指标分析**：
  - KDJ指标计算和分析（支持多个时间周期）
  - 网格交易策略分析
- **自动交易**：
  - 根据技术指标自动生成买入/卖出信号
  - 支持限价单交易
  - 买入价格支持自定义折扣（通过.env文件配置）
- **测试网支持**：默认使用Binance测试网，安全测试策略

## 安装

### 前提条件

- Python 3.7+
- Binance测试网账户和API密钥

### 安装依赖

```bash
pip install python-binance pandas numpy requests python-dotenv matplotlib
```

### 配置

1. 在项目根目录创建`.env`文件，包含以下内容：

```
API_KEY=你的测试网API_KEY
API_SECRET=你的测试网API_SECRET
SYMBOL=BTCUSDT
TRADE_QTY=0.001

# 买入折扣百分比（例如 90 表示按现价的90%下单）
BUY_DISCOUNT_PERCENT=90

# 3分钟取消下单
ORDER_TIMEOUT_MINUTES=3
```

## 使用方法

### 测试API连接

在开始交易前，建议先测试API连接是否正常：

```bash
python test_api.py
```

### 启动自动交易

```bash
python auto_trade_binan.py
```

程序将持续运行，监控市场并根据策略自动交易。

## 文件说明

- `auto_trade_binan.py`：主程序，实现自动交易功能
- `grid_strategy.py`：网格交易策略模块
- `test_api.py`：API连接测试工具
- `.env`：配置文件，包含API密钥和交易参数

## 交易策略说明

### KDJ策略

KDJ指标是一种常用的技术分析工具，用于判断价格趋势和动量。

- **金叉信号（买入）**：K线从下向上穿过D线，且处于超卖区域（K<20, D<20）
- **死叉信号（卖出）**：K线从上向下穿过D线，且处于超买区域（K>80, D>80）
- **超买信号**：K和D都大于80，J大于100
- **超卖信号**：K和D都小于20，J小于0

### 网格交易策略

网格交易是一种在价格波动中获利的策略，通过在预设价格区间内设置多个网格，在价格下跌时买入，价格上涨时卖出。

- 当价格处于网格的低位（<30%）时，产生买入信号
- 当价格处于网格的高位（>70%）时，产生卖出信号
- 当价格处于网格的中间位置时，保持观望

## 注意事项

- 本系统默认使用Binance测试网，如需使用实盘交易，请修改`client.API_URL`
- 交易有风险，请在充分测试后再使用实盘资金
- 建议先使用小额资金测试策略效果
- 系统会生成日志文件`trading_log.txt`，记录所有交易活动

## 自定义配置

### 调整KDJ参数

可以在`auto_trade_binan.py`中修改KDJ计算参数：

```python
# 修改KDJ参数
df = calculate_kdj(df, n=9, m1=3, m2=3)
```

### 调整网格参数

可以在`auto_trade_binan.py`中修改网格策略参数：

```python
# 修改网格参数
grid_levels = calculate_grid_levels(current_price, grid_count=10, grid_range=0.1)
```

## 扩展功能

- 添加更多技术指标（如MACD、RSI等）
- 实现更复杂的交易策略
- 添加风险管理功能
- 添加回测功能
- 添加图形界面

## 许可证

MIT

## 免责声明

本项目仅供学习和研究使用，不构成投资建议。使用本系统进行交易的风险由用户自行承担。
