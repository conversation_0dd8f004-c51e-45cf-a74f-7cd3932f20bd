#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试WebSocket K线数据接收
"""

import websocket
import json
import threading
import time
from datetime import datetime
from collections import deque
import pandas as pd

# 数据管理器
class TestKlineManager:
    def __init__(self):
        self.kline_data = {
            '1m': deque(maxlen=10),  # 只保留最近10根K线用于测试
            '3m': deque(maxlen=10),
            '5m': deque(maxlen=10),
            '15m': deque(maxlen=10),
            '30m': deque(maxlen=10)
        }
        self.current_price = 0.0
        
    def add_kline(self, interval, kline_data):
        """添加K线数据"""
        if interval in self.kline_data:
            self.kline_data[interval].append(kline_data)
            
    def get_klines_count(self, interval):
        """获取K线数据数量"""
        return len(self.kline_data.get(interval, []))
        
    def update_current_price(self, price):
        """更新当前价格"""
        self.current_price = float(price)

# 全局管理器
test_manager = TestKlineManager()

def on_kline_message(ws, message):
    """处理K线WebSocket消息"""
    try:
        data = json.loads(message)
        if 'k' in data:
            kline = data['k']
            interval = kline['i']
            
            # 更新当前价格
            current_price = float(kline['c'])
            test_manager.update_current_price(current_price)
            
            # 只处理已完成的K线
            if kline['x']:  # x表示K线是否已完成
                kline_data = [
                    kline['t'],  # 开盘时间
                    kline['o'],  # 开盘价
                    kline['h'],  # 最高价
                    kline['l'],  # 最低价
                    kline['c'],  # 收盘价
                    kline['v'],  # 成交量
                    kline['T'],  # 收盘时间
                    kline['q'],  # 成交额
                    kline['n'],  # 成交笔数
                    kline['V'],  # 主动买入成交量
                    kline['Q'],  # 主动买入成交额
                    '0'  # 忽略字段
                ]
                test_manager.add_kline(interval, kline_data)
                print(f"{datetime.now().strftime('%H:%M:%S')} - 新{interval}K线完成: 价格={current_price}")
                
                # 显示当前各周期的K线数量
                counts = {k: test_manager.get_klines_count(k) for k in test_manager.kline_data.keys()}
                print(f"  当前K线数量: {counts}")
            
    except Exception as e:
        print(f"处理K线消息失败: {e}")

def on_ticker_message(ws, message):
    """处理价格WebSocket消息"""
    try:
        data = json.loads(message)
        if 'c' in data:  # c是当前价格
            current_price = float(data['c'])
            test_manager.update_current_price(current_price)
            # 减少输出频率
            # print(f"价格更新: {current_price}")
    except Exception as e:
        print(f"处理价格消息失败: {e}")

def on_message(ws, message):
    """处理WebSocket消息"""
    try:
        data = json.loads(message)
        
        # 处理组合流数据
        if 'stream' in data and 'data' in data:
            stream_name = data['stream']
            stream_data = data['data']
            
            if '@kline_' in stream_name:
                # 处理K线数据
                on_kline_message(ws, json.dumps(stream_data))
            elif '@ticker' in stream_name:
                # 处理价格数据
                on_ticker_message(ws, json.dumps(stream_data))
        # 处理单一流数据
        elif 'k' in data:
            on_kline_message(ws, message)
        elif 'c' in data:
            on_ticker_message(ws, message)
            
    except Exception as e:
        print(f"处理消息失败: {e}")

def on_error(ws, error):
    """WebSocket错误处理"""
    print(f"WebSocket错误: {error}")

def on_close(ws, close_status_code, close_msg):
    """WebSocket关闭处理"""
    print("WebSocket连接已关闭")

def on_open(ws):
    """WebSocket连接打开"""
    print("WebSocket连接已建立")

def test_websocket_klines():
    """测试WebSocket K线数据"""
    symbol = "arbusdt"
    
    # 使用组合流获取K线和价格数据
    streams = [
        f"{symbol}@kline_1m",
        f"{symbol}@kline_3m", 
        f"{symbol}@kline_5m",
        f"{symbol}@kline_15m",
        f"{symbol}@kline_30m",
        f"{symbol}@ticker"
    ]
    
    # 组合流URL
    stream_url = "wss://stream.binance.com:9443/stream?streams=" + "/".join(streams)
    
    print(f"连接URL: {stream_url}")
    
    # 创建WebSocket连接
    ws = websocket.WebSocketApp(stream_url,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close,
                                on_open=on_open)
    
    # 在单独线程中运行
    def run_ws():
        ws.run_forever()
    
    ws_thread = threading.Thread(target=run_ws, daemon=True)
    ws_thread.start()
    
    return ws

if __name__ == "__main__":
    print("=" * 60)
    print("WebSocket K线数据测试")
    print("=" * 60)
    
    try:
        ws = test_websocket_klines()
        
        # 运行5分钟，观察K线数据接收情况
        print("运行5分钟，观察K线数据接收...")
        for i in range(300):  # 5分钟 = 300秒
            time.sleep(1)
            if i % 30 == 0:  # 每30秒显示一次状态
                print(f"\n--- {i//60}分{i%60}秒 状态报告 ---")
                print(f"当前价格: {test_manager.current_price}")
                for interval in ['1m', '3m', '5m', '15m', '30m']:
                    count = test_manager.get_klines_count(interval)
                    print(f"{interval}: {count}根K线")
        
        print("\n测试完成")
        
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")
