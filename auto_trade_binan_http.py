#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Binance自动交易系统
功能：
1. 行情监控
2. 基于通达信策略的交易信号生成
3. BTC限价单买入及卖出
4. 根据.env文件中的折扣参数计算买入价格
"""

print("脚本开始执行...")

import pandas as pd
import numpy as np
import time
import logging
import os
import json
from datetime import datetime
import requests
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
import traceback

# 导入数学和统计相关库
import math
import numpy as np

# 导入高级交易策略
from Trading_Strategy_Advanced import AdvancedTradingStrategy, analyze_multi_timeframe

# ----------------- 配置加载 --------------------

# 加载.env文件中的环境变量
load_dotenv()

# API配置
API_KEY = os.getenv('API_KEY')
API_SECRET = os.getenv('API_SECRET')

# 交易配置
SYMBOL = os.getenv('SYMBOL', 'ARBUSDT')  # 默认使用 ARBUSDT
TRADE_QTY = float(os.getenv('TRADE_QTY', '0.001'))

# 买入折扣百分比（例如90表示按现价的90%下单）
BUY_DISCOUNT_PERCENT = float(os.getenv('BUY_DISCOUNT_PERCENT', '100'))
if not (0 <= BUY_DISCOUNT_PERCENT <= 100):
    raise ValueError("BUY_DISCOUNT_PERCENT 必须在 0 到 100 之间")

# 订单超时时间（分钟）
ORDER_TIMEOUT_MINUTES = int(os.getenv('ORDER_TIMEOUT_MINUTES', '3'))

# 持仓状态跟踪
POSITION_STATUS = {
    'has_position': False,  # 是否持有仓位
    'entry_price': 0.0,     # 买入价格
    'quantity': 0.0,        # 持有数量
    'stop_loss': 0.0,       # 止损价格
    'entry_time': None,     # 买入时间
    'stop_loss_percent': 0.5  # 止损百分比，价格下降0.5%时触发止损
}

# 持仓状态文件
POSITION_FILE = 'position_status.json'

# 持仓时间警告阈值（小时）
POSITION_TIME_WARNING = 24  # 持仓超过24小时发出警告

# 日志配置
LOG_FILE = 'trading_log.txt'

# 先清空日志文件
with open(LOG_FILE, 'w', encoding='utf-8') as f:
    f.write("")

# 配置日志
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8',  # 添加编码设置
)

# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

# 初始化Binance客户端（正式环境）
try:
    client = Client(API_KEY, API_SECRET)
    # 不要手动设置API_URL，让库使用默认的正式环境地址

    # 测试连接
    client.ping()

    # 测试API权限
    try:
        # 尝试获取账户信息，这需要API有读取权限
        account_info = client.get_account()
        print("API有读取账户信息的权限")

        # 检查交易权限
        permissions = account_info.get('permissions', [])
        if 'SPOT' in permissions:
            print("API有现货交易权限")
        else:
            print("警告: API没有现货交易权限，无法执行实际交易")
            print("请在Binance API管理页面启用'允许现货和杠杆交易'权限")
    except Exception as e:
        print(f"API权限检查失败: {e}")
        print("可能原因: API密钥权限不足，IP限制，或API密钥已过期")
        print("建议: 检查API密钥设置，或创建新的API密钥")

    print("成功连接到Binance API（正式环境）")
except Exception as e:
    print(f"连接Binance API失败: {e}")
    client = None

# ----------------- 辅助函数 --------------------

def log_info(message):
    """记录信息到日志文件和控制台"""
    try:
        # print(f"[INFO] {message}")
        logging.info(message)
    except Exception as e:
        print(f"日志记录错误: {e}")

def log_error(message):
    """记录错误到日志文件和控制台"""
    try:
        print(f"[ERROR] {message}")
        logging.error(message)
    except Exception as e:
        print(f"日志记录错误: {e}")

# 加载持仓状态
def load_position_status():
    """从文件加载持仓状态"""
    global POSITION_STATUS
    try:
        if os.path.exists(POSITION_FILE):
            with open(POSITION_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 转换entry_time字符串为datetime对象
                if data.get('entry_time'):
                    data['entry_time'] = datetime.fromisoformat(data['entry_time'])
                POSITION_STATUS.update(data)
                print(f"已从文件加载持仓状态: {POSITION_STATUS['has_position']}")
    except Exception as e:
        print(f"加载持仓状态失败: {e}")

# 保存持仓状态
def save_position_status():
    """保存持仓状态到文件"""
    try:
        # 创建一个副本，避免修改原始数据
        data = POSITION_STATUS.copy()
        # 转换datetime对象为ISO格式字符串
        if data.get('entry_time'):
            data['entry_time'] = data['entry_time'].isoformat()

        with open(POSITION_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        log_info("持仓状态已保存到文件")
    except Exception as e:
        log_error(f"保存持仓状态失败: {e}")

def get_current_price(symbol=SYMBOL):
    """获取当前价格"""
    try:
        ticker = client.get_symbol_ticker(symbol=symbol)
        return float(ticker['price'])
    except Exception as e:
        log_error(f"获取当前价格失败: {e}")
        return None

def get_klines(symbol=SYMBOL, interval='1m', limit=100):
    """获取K线数据"""
    try:
        klines = client.get_klines(symbol=symbol, interval=interval, limit=limit)

        # 转换为DataFrame
        df = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ])

        # 转换数据类型
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)

        return df
    except Exception as e:
        log_error(f"获取K线数据失败: {e}")
        return None

# ----------------- 策略辅助函数 --------------------

def prepare_data_for_strategy(klines_df):
    """
    将Binance K线数据转换为策略所需的格式

    参数:
        klines_df: DataFrame, 从Binance获取的K线数据

    返回:
        DataFrame: 转换后的数据
    """
    if klines_df is None or klines_df.empty:
        return None

    # 创建新的DataFrame
    df = pd.DataFrame()

    # 复制必要的列并重命名
    df['open'] = klines_df['open']
    df['high'] = klines_df['high']
    df['low'] = klines_df['low']
    df['close'] = klines_df['close']
    df['volume'] = klines_df['volume']

    # 设置索引
    df.index = klines_df['timestamp']

    return df

def calculate_trend_line(df, n=55, m1=5, m2=3, m3=3):
    """
    计算趋势线指标 (基于通达信策略)

    参数:
        df: DataFrame, 包含OHLC数据
        n: int, 周期
        m1: int, SMA周期1
        m2: int, SMA周期2
        m3: int, EMA周期

    返回:
        Series: 趋势线指标
    """
    if df is None or df.empty:
        return None

    # 确保数据量足够
    if len(df) < n + m1 + m2 + m3:
        # 如果数据不足，调整周期
        adjusted_n = min(n, max(10, len(df) // 3))  # 至少使用10个周期，最多使用原周期
        log_info(f"数据量不足，将周期从 {n} 调整为 {adjusted_n}")
        n = adjusted_n

    # 计算最低价的n周期最低值
    low_min = df['low'].rolling(window=n).min()

    # 计算最高价的n周期最高值
    high_max = df['high'].rolling(window=n).max()

    # 计算RSV值，处理除零情况
    rsv = np.zeros(len(df))
    for i in range(n-1, len(df)):
        if high_max.iloc[i] - low_min.iloc[i] != 0:
            rsv[i] = (df['close'].iloc[i] - low_min.iloc[i]) / (high_max.iloc[i] - low_min.iloc[i]) * 100
        else:
            # 如果最高价等于最低价，RSV设为50
            rsv[i] = 50

    rsv_series = pd.Series(rsv, index=df.index)

    # 计算V11 (3*SMA(RSV,5,1)-2*SMA(SMA(RSV,5,1),3,1))
    sma1 = rsv_series.rolling(window=m1, min_periods=1).mean()
    sma2 = sma1.rolling(window=m2, min_periods=1).mean()
    v11 = 3 * sma1 - 2 * sma2

    # 计算趋势线 (EMA(V11,3))
    trend_line = v11.ewm(span=m3, min_periods=1, adjust=False).mean()

    # 填充NaN值
    trend_line = trend_line.fillna(50)  # 使用50作为默认值

    return trend_line

def analyze_advanced_strategy(df):
    """
    使用高级交易策略分析市场

    参数:
        df: DataFrame, 包含OHLC数据

    返回:
        dict: 分析结果
    """
    if df is None or df.empty:
        return {
            'signals': [],
            'recommendation': 'HOLD'
        }

    # 创建高级策略实例
    strategy = AdvancedTradingStrategy(df)

    # 获取市场状态
    market_status = strategy.get_market_status()

    # 返回分析结果
    return market_status

# ----------------- 交易功能 --------------------

def place_limit_order(symbol, side, quantity, price):
    """下限价单

    参数:
        symbol: 交易对
        side: 方向，'BUY' 或 'SELL'
        quantity: 数量
        price: 价格

    返回:
        order: 订单信息
    """

    # 实际下单
    try:
        order = client.create_order(
            symbol=symbol,
            side=side,
            type='LIMIT',
            timeInForce='GTC',
            quantity=quantity,
            price=round(price, 6)  # 增加精度
        )
        log_info(f"下单成功: {side} {quantity} {symbol} @ {price}")
        return order
    except BinanceAPIException as e:
        error_code = getattr(e, 'code', None)
        if error_code == -2015:
            log_error(f"下单失败: API密钥权限不足或IP限制 - {e}")
            log_info("请检查: 1. API密钥是否有交易权限 2. 是否设置了IP白名单 3. API密钥是否过期")
        elif error_code == -1013:
            log_error(f"下单失败: 交易数量或价格精度不符合要求 - {e}")
            log_info(f"建议: 检查{symbol}的最小交易数量和价格精度要求")
        elif error_code == -2010:
            log_error(f"下单失败: 余额不足 - {e}")
            log_info("建议: 确保账户有足够的资金")
        else:
            log_error(f"下单失败: {e}")
        return None
    except Exception as e:
        log_error(f"下单异常: {e}")
        return None

def place_market_order(symbol, side, quantity):
    """下市价单

    参数:
        symbol: 交易对
        side: 方向，'BUY' 或 'SELL'
        quantity: 数量

    返回:
        order: 订单信息
    """

    # 实际下单
    try:
        order = client.create_order(
            symbol=symbol,
            side=side,
            type='MARKET',
            quantity=quantity
        )
        log_info(f"市价单下单成功: {side} {quantity} {symbol}")
        return order
    except BinanceAPIException as e:
        error_code = getattr(e, 'code', None)
        if error_code == -2015:
            log_error(f"市价单下单失败: API密钥权限不足或IP限制 - {e}")
            log_info("请检查: 1. API密钥是否有交易权限 2. 是否设置了IP白名单 3. API密钥是否过期")
        elif error_code == -1013:
            log_error(f"市价单下单失败: 交易数量不符合要求 - {e}")
            log_info(f"建议: 检查{symbol}的最小交易数量要求")
        elif error_code == -2010:
            log_error(f"市价单下单失败: 余额不足 - {e}")
            log_info("建议: 确保账户有足够的资金")
        else:
            log_error(f"市价单下单失败: {e}")
        return None
    except Exception as e:
        log_error(f"市价单下单异常: {e}")
        return None

def cancel_order(symbol, order_id):
    """取消订单

    参数:
        symbol: 交易对
        order_id: 订单ID

    返回:
        result: 取消结果
    """
    # if TEST_MODE:
    #     # 测试模式，只记录取消订单信息
    #     log_info(f"[测试模式] 模拟取消订单: {order_id}")
    #     return {'orderId': order_id, 'status': 'CANCELED'}

    # 实际取消订单
    try:
        result = client.cancel_order(
            symbol=symbol,
            orderId=order_id
        )
        log_info(f"取消订单成功: {order_id}")
        return result
    except Exception as e:
        log_error(f"取消订单失败: {e}")
        return None

def check_order_status(symbol, order_id):
    """检查订单状态

    参数:
        symbol: 交易对
        order_id: 订单ID

    返回:
        status: 订单状态
    """
    # if TEST_MODE:
    #     # 测试模式，随机返回订单状态
    #     # 模拟80%的概率订单成交
    #     if time.time() % 10 < 8:  # 使用时间的个位数模拟随机
    #         return 'FILLED'
    #     else:
    #         return 'NEW'

    # 实际检查订单状态
    try:
        order = client.get_order(
            symbol=symbol,
            orderId=order_id
        )
        return order['status']
    except Exception as e:
        log_error(f"检查订单状态失败: {e}")
        return None

# ----------------- 主程序 --------------------

def check_stop_loss():
    """检查止损条件，如果触发则执行市价卖出"""
    if not POSITION_STATUS['has_position']:
        return False  # 没有持仓，不需要检查止损

    current_price = get_current_price()
    if not current_price:
        log_error("无法获取当前价格，无法检查止损")
        return False

    # 计算止损价格（买入价格的0.5%下跌）
    stop_loss_price = POSITION_STATUS['entry_price'] * (1 - POSITION_STATUS['stop_loss_percent'] / 100)

    # 检查是否触发止损
    if current_price <= stop_loss_price:
        log_info(f"触发止损! 当前价格 {current_price:.4f} 低于止损价格 {stop_loss_price:.4f}")

        # 执行市价卖出
        order = place_market_order(SYMBOL, 'SELL', POSITION_STATUS['quantity'])

        if order:
            # 计算损失
            loss = (current_price - POSITION_STATUS['entry_price']) * POSITION_STATUS['quantity']
            loss_percent = (current_price / POSITION_STATUS['entry_price'] - 1) * 100

            log_info(f"止损卖出成功: {POSITION_STATUS['quantity']} {SYMBOL} @ 市价(约 {current_price:.4f})")
            log_info(f"止损结果: 买入价格 {POSITION_STATUS['entry_price']:.4f}, 卖出价格 {current_price:.4f}")
            log_info(f"损失: {loss:.4f} USDT ({loss_percent:.2f}%)")

            # 重置持仓状态
            POSITION_STATUS['has_position'] = False
            POSITION_STATUS['entry_price'] = 0.0
            POSITION_STATUS['quantity'] = 0.0
            POSITION_STATUS['stop_loss'] = 0.0
            POSITION_STATUS['entry_time'] = None

            # 保存持仓状态
            save_position_status()

            log_info("持仓状态已重置，可以寻找新的买入信号")
            return True

    return False

def check_position_time():
    """检查持仓时间，如果超过阈值则发出警告"""
    if not POSITION_STATUS['has_position'] or not POSITION_STATUS['entry_time']:
        return

    # 计算持仓时间
    holding_time = datetime.now() - POSITION_STATUS['entry_time']
    holding_hours = holding_time.total_seconds() / 3600

    # 检查是否超过警告阈值
    if holding_hours >= POSITION_TIME_WARNING:
        log_info(f"警告: 持仓时间已超过 {POSITION_TIME_WARNING} 小时! 当前持仓时间: {holding_hours:.1f} 小时")
        log_info(f"当前持仓: {POSITION_STATUS['quantity']} {SYMBOL} @ {POSITION_STATUS['entry_price']:.4f}")

        # 获取当前价格计算盈亏
        current_price = get_current_price()
        if current_price:
            profit = (current_price - POSITION_STATUS['entry_price']) * POSITION_STATUS['quantity']
            profit_percent = (current_price / POSITION_STATUS['entry_price'] - 1) * 100
            log_info(f"当前价格: {current_price:.4f}, 浮动盈亏: {profit:.4f} USDT ({profit_percent:.2f}%)")

def main():
    """主程序"""
    # 验证API密钥是否有效 - 这个检查仍然保留，以防API密钥在运行过程中被修改
    if not API_KEY or not API_SECRET or API_KEY == 'placeholder_api_key' or API_SECRET == 'placeholder_api_secret':
        log_error("API密钥未设置或是占位符，请在.env文件中设置有效的API密钥")
        return

    # 检查客户端是否初始化成功
    if client is None:
        log_error("Binance客户端初始化失败，无法继续")
        return

    # 检查止损条件
    if check_stop_loss():
        log_info("已执行止损，本轮分析结束")
        return

    # 检查持仓时间
    check_position_time()

    try:

        # 获取当前价格
        current_price = get_current_price()
        if not current_price:
            log_error("无法获取当前价格，程序退出")
            return

        log_info(f"当前{SYMBOL}价格: {current_price}")

        # 获取不同时间周期的K线数据
        intervals = ['1m', '3m', '5m', '15m', '30m']  # 增加1分钟和5分钟周期进行分析
        # 为不同周期设置不同的K线数量
        interval_limits = {
            '1m': 500,   # 1分钟周期获取500根K线
            '3m': 300,   # 3分钟周期获取300根K线
            '5m': 400,   # 5分钟周期获取400根K线
            '15m': 250,  # 15分钟周期获取250根K线
            '30m': 200   # 30分钟周期获取200根K线
        }
        analysis_results = {}

        for interval in intervals:
            # 获取K线数据，使用为每个周期定义的限制
            limit = interval_limits.get(interval, 100)
            klines_df = get_klines(interval=interval, limit=limit)
            if klines_df is None or klines_df.empty:
                log_error(f"无法获取{interval}周期的K线数据")
                continue

            # 检查数据量是否足够
            if len(klines_df) < 60:  # 确保至少有60根K线
                log_info(f"{interval}周期数据量不足，实际获取到 {len(klines_df)} 根K线")
                # 尝试获取更多历史数据
                try:
                    # 根据周期设置不同的历史数据范围
                    if interval == '1m':
                        start_str = "12 hours ago UTC"
                    elif interval == '3m':
                        start_str = "1 day ago UTC"
                    elif interval == '5m':
                        start_str = "18 hours ago UTC"
                    elif interval == '15m':
                        start_str = "3 days ago UTC"
                    else:  # 30m
                        start_str = "5 days ago UTC"

                    log_info(f"尝试获取更多的{interval}周期数据...")
                    more_klines = client.get_historical_klines(
                        symbol=SYMBOL,
                        interval=interval,
                        start_str=start_str,
                        limit=1000  # 增加限制
                    )
                    if more_klines:
                        # 转换为DataFrame
                        more_df = pd.DataFrame(more_klines, columns=[
                            'timestamp', 'open', 'high', 'low', 'close', 'volume',
                            'close_time', 'quote_asset_volume', 'number_of_trades',
                            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
                        ])

                        # 转换数据类型
                        more_df['timestamp'] = pd.to_datetime(more_df['timestamp'], unit='ms')
                        more_df['open'] = more_df['open'].astype(float)
                        more_df['high'] = more_df['high'].astype(float)
                        more_df['low'] = more_df['low'].astype(float)
                        more_df['close'] = more_df['close'].astype(float)
                        more_df['volume'] = more_df['volume'].astype(float)

                        klines_df = more_df
                        log_info(f"成功获取到 {len(klines_df)} 根{interval}周期K线")
                except Exception as e:
                    log_error(f"获取更多{interval}周期数据失败: {e}")

            # 准备数据
            strategy_df = prepare_data_for_strategy(klines_df)
            if strategy_df is None:
                log_error(f"无法准备{interval}周期的策略数据")
                continue

            # 使用高级交易策略分析市场
            result = analyze_advanced_strategy(strategy_df)
            analysis_results[interval] = result

            # 不输出市场状态信息
            # 市场状态: {result['market_status']}

            # 记录信号详情 - 不再单独打印每个信号

        # 综合分析不同时间周期的结果
        if not analysis_results:
            log_error("没有足够的分析结果，无法生成交易信号")
            return

        # 使用多周期加权分析
        final_recommendation = analyze_multi_timeframe(analysis_results)

        # 生成综合建议
        if final_recommendation == 'BUY':
            # 买入信号
            log_info("综合分析: ★★★★ 买入信号 ★★★★  买买买买买买买买买买买买")

            # 检查是否已经持有仓位
            if POSITION_STATUS['has_position']:
                log_info("已持有仓位，忽略买入信号")
                log_info(f"当前持仓: 价格 {POSITION_STATUS['entry_price']:.4f}, 数量 {POSITION_STATUS['quantity']}, 止损点 {POSITION_STATUS['stop_loss']:.4f}")
                return

            # 获取最佳买入价格和止损点
            best_buy_signal = None
            for result in analysis_results.values():
                for signal in result['signals']:
                    if signal[0] == 'buy':
                        if best_buy_signal is None or signal[1] > best_buy_signal[1]:
                            best_buy_signal = signal

            if best_buy_signal:
                # 获取信号详情
                price = best_buy_signal[1]
                stop_loss = best_buy_signal[3]
                log_info(f"最佳买入信号: 价格 {price:.4f}, 止损点 {stop_loss:.4f}")

                # 应用折扣
                buy_price = price * (BUY_DISCOUNT_PERCENT / 100)

                log_info(f"最佳买入价格: {buy_price:.4f}, 止损点: {stop_loss:.4f}")

                # 下买入限价单
                order = place_limit_order(SYMBOL, 'BUY', TRADE_QTY, buy_price)

                if order:
                    log_info(f"买入订单已提交: {order['orderId']}")

                    # 监控订单状态
                    start_time = time.time()
                    while time.time() - start_time < ORDER_TIMEOUT_MINUTES * 60:
                        status = check_order_status(SYMBOL, order['orderId'])

                        if status == 'FILLED':
                            log_info(f"买入订单已成交")

                            # 更新持仓状态
                            POSITION_STATUS['has_position'] = True
                            POSITION_STATUS['entry_price'] = buy_price
                            POSITION_STATUS['quantity'] = TRADE_QTY
                            POSITION_STATUS['stop_loss'] = stop_loss
                            POSITION_STATUS['entry_time'] = datetime.now()

                            # 计算止损价格
                            stop_loss_price = buy_price * (1 - POSITION_STATUS['stop_loss_percent'] / 100)
                            log_info(f"设置止损价格: {stop_loss_price:.4f} (买入价格下跌 {POSITION_STATUS['stop_loss_percent']}%)")

                            # 保存持仓状态到文件
                            save_position_status()

                            log_info(f"更新持仓状态: 已买入 {TRADE_QTY} {SYMBOL} @ {buy_price:.4f}")
                            break

                        elif status == 'CANCELED' or status == 'REJECTED' or status == 'EXPIRED':
                            log_info(f"买入订单未成交: {status}")
                            break

                        time.sleep(10)  # 每10秒检查一次

                    # 超时取消订单
                    if time.time() - start_time >= ORDER_TIMEOUT_MINUTES * 60:
                        log_info(f"买入订单超时，准备取消")
                        cancel_order(SYMBOL, order['orderId'])
            else:
                log_info("没有找到合适的买入信号")

        elif final_recommendation == 'SELL':
            # 卖出信号
            log_info("综合分析: ☆☆☆☆ 卖出信号 ☆☆☆☆  卖卖卖卖卖卖")

            # 检查是否持有仓位
            if not POSITION_STATUS['has_position']:
                log_info("没有持仓，忽略卖出信号")
                return

            # 获取最佳卖出价格和止损点（忽略1分钟周期）
            best_sell_signal = None
            for interval, result in analysis_results.items():
                # 跳过1分钟周期的卖出信号，以避免因短期波动而提前卖出
                if interval == '1m':
                    continue

                for signal in result['signals']:
                    if signal[0] == 'sell':
                        if best_sell_signal is None or signal[1] < best_sell_signal[1]:
                            best_sell_signal = signal

            # 如果没有找到卖出信号，使用当前价格
            if best_sell_signal:
                # 获取信号详情
                price = best_sell_signal[1]
                stop_loss = best_sell_signal[3]
                log_info(f"最佳卖出信号: 价格 {price:.4f}, 止损点 {stop_loss:.4f}")

                # 卖出价格略低于信号价格
                sell_price = price * 0.999
            else:
                # 如果没有找到卖出信号，使用当前价格
                sell_price = current_price
                log_info(f"没有找到卖出信号，使用当前价格: {sell_price:.4f}")

            log_info(f"卖出价格: {sell_price:.4f}")

            # 计算持仓收益
            profit = (sell_price - POSITION_STATUS['entry_price']) * POSITION_STATUS['quantity']
            profit_percent = (sell_price / POSITION_STATUS['entry_price'] - 1) * 100
            log_info(f"预计收益: {profit:.4f} USDT ({profit_percent:.2f}%)")

            # 下卖出限价单
            order = place_limit_order(SYMBOL, 'SELL', POSITION_STATUS['quantity'], sell_price)

            if order:
                log_info(f"卖出订单已提交: {order['orderId']}")

                # 监控订单状态
                start_time = time.time()
                while time.time() - start_time < ORDER_TIMEOUT_MINUTES * 60:
                    status = check_order_status(SYMBOL, order['orderId'])

                    if status == 'FILLED':
                        log_info(f"卖出订单已成交")

                        # 计算实际收益
                        actual_profit = (sell_price - POSITION_STATUS['entry_price']) * POSITION_STATUS['quantity']
                        actual_profit_percent = (sell_price / POSITION_STATUS['entry_price'] - 1) * 100

                        # 记录交易结果
                        holding_time = datetime.now() - POSITION_STATUS['entry_time']
                        log_info(f"交易结果: 买入价格 {POSITION_STATUS['entry_price']:.4f}, 卖出价格 {sell_price:.4f}")
                        log_info(f"收益: {actual_profit:.4f} USDT ({actual_profit_percent:.2f}%)")
                        log_info(f"持仓时间: {holding_time}")

                        # 重置持仓状态
                        POSITION_STATUS['has_position'] = False
                        POSITION_STATUS['entry_price'] = 0.0
                        POSITION_STATUS['quantity'] = 0.0
                        POSITION_STATUS['stop_loss'] = 0.0
                        POSITION_STATUS['entry_time'] = None

                        # 保存持仓状态到文件
                        save_position_status()

                        log_info("持仓状态已重置，可以寻找新的买入信号")
                        break

                    elif status == 'CANCELED' or status == 'REJECTED' or status == 'EXPIRED':
                        log_info(f"卖出订单未成交: {status}")
                        break

                    time.sleep(10)  # 每10秒检查一次

                # 超时取消订单
                if time.time() - start_time >= ORDER_TIMEOUT_MINUTES * 60:
                    log_info(f"卖出订单超时，准备取消")
                    cancel_order(SYMBOL, order['orderId'])

        else:
            # 观望信号
            log_info("综合分析: 观望信号，不进行交易")

        # 输出市场分析（美化格式）
        log_info("市场分析:")

        # 按照时间周期排序（1m, 3m, 5m, 15m, 30m）
        sorted_intervals = sorted(analysis_results.keys(),
                                 key=lambda x: {'1m': 1, '3m': 2, '5m': 3, '15m': 4, '30m': 5}.get(x, 10))

        for interval in sorted_intervals:
            result = analysis_results[interval]

            # 获取市场环境
            market_env = result.get('market_env', 'unknown')

            # 获取指标数据
            indicators = result.get('indicators', {})

            # 获取建议
            recommendation = result.get('recommendation', 'HOLD')

            # 设置市场环境描述
            if market_env == 'trend':
                env_desc = "趋势"
            elif market_env == 'range':
                env_desc = "震荡"
            elif market_env == 'volatile':
                env_desc = "多变"
            else:
                env_desc = "未知"

            # 获取趋势线值和状态
            trend_status = ""
            trend_value = indicators.get('trend_line', None)
            if trend_value is not None and not pd.isna(trend_value):
                if trend_value < 20:
                    trend_status = "超卖"
                elif trend_value > 80:
                    trend_status = "超买"
                else:
                    trend_status = "中性"
                trend_str = f"趋势线:{trend_value:.2f}({trend_status})"
            else:
                trend_str = "趋势线:无数据"

            # 获取RSI值
            rsi_value = indicators.get('rsi', None)
            if rsi_value is not None and not pd.isna(rsi_value):
                rsi_str = f"RSI:{rsi_value:.2f}"
            else:
                rsi_str = "RSI:无数据"

            # 构建美化的输出行
            output_line = f"{interval}: 环境:{env_desc} | {trend_str} | {rsi_str} | 建议:{recommendation}"

            # 输出到日志
            log_info(output_line)

    except Exception as e:
        error_msg = f"程序执行异常: {e}"
        log_error(error_msg)
        # 记录详细的错误堆栈
        error_traceback = traceback.format_exc()
        log_error(f"错误详情:\n{error_traceback}")

if __name__ == "__main__":
    print("程序开始执行...")

    # 记录启动信息到日志文件
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()} - 程序开始执行...\n")

    # 加载持仓状态
    load_position_status()

    # 打印固定参数信息（只打印一次）
    log_info("程序启动，开始监控市场...")
    log_info(f"使用交易对: {SYMBOL}")
    log_info(f"买入折扣百分比: {BUY_DISCOUNT_PERCENT}%")
    log_info(f"每次交易数量: {TRADE_QTY}")

    # 测试API连接（只测试一次）
    log_info("测试API连接...")
    try:
        client.ping()
        log_info("API连接正常")
    except Exception as e:
        log_error(f"API连接失败: {e}")
        exit(1)  # 如果API连接失败，直接退出程序

    # 设置最大运行次数
    max_runs = 5000
    run_count = 0

    while run_count < max_runs:
        try:
            print(f"执行第 {run_count + 1} 次...")

            # 记录到日志文件
            with open(LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now()} - 执行第 {run_count + 1} 次...\n")

            # 修改main函数调用，不再打印固定参数
            main()

            # 显示当前持仓状态
            if POSITION_STATUS['has_position']:
                current_price = get_current_price()
                if current_price:
                    profit = (current_price - POSITION_STATUS['entry_price']) * POSITION_STATUS['quantity']
                    profit_percent = (current_price / POSITION_STATUS['entry_price'] - 1) * 100
                    holding_time = datetime.now() - POSITION_STATUS['entry_time']

                    # 计算止损价格
                    stop_loss_price = POSITION_STATUS['entry_price'] * (1 - POSITION_STATUS['stop_loss_percent'] / 100)

                    log_info(f"当前持仓: {POSITION_STATUS['quantity']} {SYMBOL} @ {POSITION_STATUS['entry_price']:.4f}")
                    log_info(f"当前价格: {current_price:.4f}, 浮动盈亏: {profit:.4f} USDT ({profit_percent:.2f}%)")
                    log_info(f"止损价格: {stop_loss_price:.4f}, 持仓时间: {holding_time}")

                    # 检查是否接近止损价格
                    if current_price < POSITION_STATUS['entry_price'] and current_price / stop_loss_price < 1.01:
                        log_info(f"警告: 当前价格接近止损价格! 距离止损仅剩 {((current_price / stop_loss_price) - 1) * 100:.2f}%")

            # 增加运行次数
            run_count += 1

            # 如果还没达到最大次数，等待一段时间
            if run_count < max_runs:
                print(f"等待 2 秒后进行下一轮...")
                time.sleep(1.5)  # 每2秒执行一次
        except KeyboardInterrupt:
            print("程序被用户中断")
            log_info("程序被用户中断")
            break
        except Exception as e:
            error_msg = f"主循环异常: {e}"
            print(error_msg)
            log_error(error_msg)

            # 记录详细的错误堆栈
            error_traceback = traceback.format_exc()
            log_error(f"错误详情:\n{error_traceback}")

            time.sleep(10)  # 发生异常后等待10秒再继续

    print("程序执行完毕，达到最大运行次数。")
