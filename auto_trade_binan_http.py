#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Binance行情监控系统
功能：
1. 行情监控
2. 基于通达信策略的交易信号生成
3. 市场分析和信号提示（仅监控，不执行交易）
"""

print("脚本开始执行...")

import pandas as pd
import numpy as np
import time
import logging
import os
from datetime import datetime
from binance.client import Client
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv
import traceback
import pyautogui
import time
from pywinauto.application import Application

# 导入高级交易策略
from Trading_Strategy_Advanced import AdvancedTradingStrategy, analyze_multi_timeframe

# ----------------- 配置加载 --------------------

# 加载.env文件中的环境变量
load_dotenv()

# API配置
API_KEY = os.getenv('API_KEY')
API_SECRET = os.getenv('API_SECRET')

# 监控配置
SYMBOL = os.getenv('SYMBOL', 'ARBUSDT')  # 默认使用 ARBUSDT
TRADE_QTY = float(os.getenv('TRADE_QTY', '10'))  # 交易数量，默认10 USDT
BUY_DISCOUNT_PERCENT = float(os.getenv('BUY_DISCOUNT_PERCENT', '99.5')) / 100  # 买入折扣百分比，默认99.5%

# 交易标志（初始值为sell）
TRADE_FLAG = 'To_buy'

# 日志配置
LOG_FILE = 'trading_log.txt'

# 先清空日志文件
with open(LOG_FILE, 'w', encoding='utf-8') as f:
    f.write("")

# 配置日志
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8',  # 添加编码设置
)

# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

# 初始化Binance客户端（正式环境）
try:
    client = Client(API_KEY, API_SECRET)
    # 不要手动设置API_URL，让库使用默认的正式环境地址

    # 测试连接
    client.ping()

    # 测试API权限
    try:
        # 尝试获取账户信息，这需要API有读取权限
        account_info = client.get_account()
        print("API有读取账户信息的权限")

        # 检查交易权限
        permissions = account_info.get('permissions', [])
        if 'SPOT' in permissions:
            print("API有现货交易权限")
        else:
            print("警告: API没有现货交易权限，无法执行实际交易")
            print("请在Binance API管理页面启用'允许现货和杠杆交易'权限")
    except Exception as e:
        print(f"API权限检查失败: {e}")
        print("可能原因: API密钥权限不足，IP限制，或API密钥已过期")
        print("建议: 检查API密钥设置，或创建新的API密钥")

    print("成功连接到Binance API（正式环境）")
except Exception as e:
    print(f"连接Binance API失败: {e}")
    client = None

# ----------------- 辅助函数 --------------------

def log_info(message):
    """记录信息到日志文件和控制台"""
    try:
        # print(f"[INFO] {message}")
        logging.info(message)
    except Exception as e:
        print(f"日志记录错误: {e}")

def log_error(message):
    """记录错误到日志文件和控制台"""
    try:
        print(f"[ERROR] {message}")
        logging.error(message)
    except Exception as e:
        print(f"日志记录错误: {e}")


def get_current_price(symbol=SYMBOL):
    """获取当前价格"""
    try:
        ticker = client.get_symbol_ticker(symbol=symbol)
        return float(ticker['price'])
    except Exception as e:
        log_error(f"获取当前价格失败: {e}")
        return None

def get_klines(symbol=SYMBOL, interval='1m', limit=100):
    """获取K线数据"""
    try:
        klines = client.get_klines(symbol=symbol, interval=interval, limit=limit)

        # 转换为DataFrame
        df = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ])

        # 转换数据类型
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['open'] = df['open'].astype(float)
        df['high'] = df['high'].astype(float)
        df['low'] = df['low'].astype(float)
        df['close'] = df['close'].astype(float)
        df['volume'] = df['volume'].astype(float)

        return df
    except Exception as e:
        log_error(f"获取K线数据失败: {e}")
        return None

# ----------------- 策略辅助函数 --------------------

def prepare_data_for_strategy(klines_df):
    """
    将Binance K线数据转换为策略所需的格式

    参数:
        klines_df: DataFrame, 从Binance获取的K线数据

    返回:
        DataFrame: 转换后的数据
    """
    if klines_df is None or klines_df.empty:
        return None

    # 创建新的DataFrame
    df = pd.DataFrame()

    # 复制必要的列并重命名
    df['open'] = klines_df['open']
    df['high'] = klines_df['high']
    df['low'] = klines_df['low']
    df['close'] = klines_df['close']
    df['volume'] = klines_df['volume']

    # 设置索引
    df.index = klines_df['timestamp']

    return df

def calculate_trend_line(df, n=55, m1=5, m2=3, m3=3):
    """
    计算趋势线指标 (基于通达信策略)

    参数:
        df: DataFrame, 包含OHLC数据
        n: int, 周期
        m1: int, SMA周期1
        m2: int, SMA周期2
        m3: int, EMA周期

    返回:
        Series: 趋势线指标
    """
    if df is None or df.empty:
        return None

    # 确保数据量足够
    if len(df) < n + m1 + m2 + m3:
        # 如果数据不足，调整周期
        adjusted_n = min(n, max(10, len(df) // 3))  # 至少使用10个周期，最多使用原周期
        log_info(f"数据量不足，将周期从 {n} 调整为 {adjusted_n}")
        n = adjusted_n

    # 计算最低价的n周期最低值
    low_min = df['low'].rolling(window=n).min()

    # 计算最高价的n周期最高值
    high_max = df['high'].rolling(window=n).max()

    # 计算RSV值，处理除零情况
    rsv = np.zeros(len(df))
    for i in range(n-1, len(df)):
        if high_max.iloc[i] - low_min.iloc[i] != 0:
            rsv[i] = (df['close'].iloc[i] - low_min.iloc[i]) / (high_max.iloc[i] - low_min.iloc[i]) * 100
        else:
            # 如果最高价等于最低价，RSV设为50
            rsv[i] = 50

    rsv_series = pd.Series(rsv, index=df.index)

    # 计算V11 (3*SMA(RSV,5,1)-2*SMA(SMA(RSV,5,1),3,1))
    sma1 = rsv_series.rolling(window=m1, min_periods=1).mean()
    sma2 = sma1.rolling(window=m2, min_periods=1).mean()
    v11 = 3 * sma1 - 2 * sma2

    # 计算趋势线 (EMA(V11,3))
    trend_line = v11.ewm(span=m3, min_periods=1, adjust=False).mean()

    # 填充NaN值
    trend_line = trend_line.fillna(50)  # 使用50作为默认值

    return trend_line

def analyze_advanced_strategy(df):
    """
    使用高级交易策略分析市场

    参数:
        df: DataFrame, 包含OHLC数据

    返回:
        dict: 分析结果
    """
    if df is None or df.empty:
        return {
            'signals': [],
            'recommendation': 'HOLD'
        }

    # 创建高级策略实例
    strategy = AdvancedTradingStrategy(df)

    # 获取市场状态
    market_status = strategy.get_market_status()

    # 返回分析结果
    return market_status


# ----------------- 交易功能 --------------------

def list_browser_windows():
    """列出当前打开的浏览器窗口"""
    try:
        from pywinauto import Desktop
        desktop = Desktop(backend="uia")
        windows = desktop.windows()

        browser_windows = []
        for window in windows:
            try:
                title = window.window_text()
                if any(browser in title.lower() for browser in ['chrome', 'edge', 'firefox', 'browser', '浏览器']):
                    browser_windows.append(title)
            except:
                continue

        if browser_windows:
            log_info("找到以下浏览器窗口:")
            for i, title in enumerate(browser_windows, 1):
                log_info(f"  {i}. {title}")
        else:
            log_info("未找到任何浏览器窗口")

        return browser_windows
    except Exception as e:
        log_error(f"列出浏览器窗口时发生错误: {e}")
        return []

# 以下是在网站买入的函数
def buy_crypto(buy_price, buy_amount):
    """
    执行买入操作（通过网页界面）

    参数:
        buy_price: str, 买入价格（字符串格式）
        buy_amount: str, 买入金额（字符串格式）
    """
    # 确保参数是字符串格式
    buy_price = str(buy_price)
    buy_amount = str(buy_amount)

    log_info(f"开始执行买入操作: 价格={buy_price}, 金额={buy_amount}")

    # 尝试连接谷歌浏览器窗口
    try:
        app = Application(backend='uia').connect(title_re='.*Chrome.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)

    # 移动鼠标到屏幕中点
    pyautogui.moveTo(960, 540)
    time.sleep(0.5)

    # 根据买入按钮图片定位
    try:
        buy_button_location = pyautogui.locateOnScreen(r'img\buy.png')

        if buy_button_location is not None:
            log_info("找到买入按钮，开始操作")

            pyautogui.moveTo(buy_button_location)   # 定位到买入按钮

            pyautogui.move(0, -570, duration=0.5)   # 定位到价格输入框
            time.sleep(1)
            pyautogui.doubleClick()                 # 双击鼠标
            pyautogui.typewrite(buy_price)           # 输入价格

            pyautogui.move(0, 160, duration=0.5)    # 定位到成交额输入框
            pyautogui.doubleClick()                 # 双击鼠标
            pyautogui.typewrite(buy_amount)         # 输入成交额数量

            # 点击买入按钮
            pyautogui.click(buy_button_location)

            global TRADE_FLAG
            TRADE_FLAG = 'To_sell'
            log_info("买入操作完成，交易标志已更新为: To_sell")

        else:
            log_error("买入按钮未找到")

    except Exception as e:
        log_error(f"买入操作过程中发生错误: {e}")

    try:
        app = Application(backend='uia').connect(title_re='.*Studio.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)

# 以下是在网站卖出的函数
def sell_crypto(sell_price, sell_amount):
    """
    执行卖出操作（通过网页界面）

    参数:
        sell_price: str, 卖出价格（字符串格式）
        sell_amount: str, 卖出金额（字符串格式）
    """
    # 确保参数是字符串格式
    sell_price = str(sell_price)
    sell_amount = str(sell_amount)

    log_info(f"开始执行卖出操作: 价格={sell_price}, 金额={sell_amount}")

    # 尝试连接浏览器窗口
    try:
        app = Application(backend='uia').connect(title_re='.*Chrome.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)

    # 移动鼠标到屏幕中点
    pyautogui.moveTo(960, 540)
    time.sleep(0.5)

    # 根据卖出按钮图片定位
    try:
        sell_button_location = pyautogui.locateOnScreen(r'img\sell.png')

        if sell_button_location is not None:
            log_info("找到卖出按钮，开始操作")

            pyautogui.moveTo(sell_button_location)   # 定位到卖出按钮

            pyautogui.move(0, -570, duration=0.5)   # 定位到价格输入框
            time.sleep(1)
            pyautogui.doubleClick()                 # 双击鼠标
            pyautogui.typewrite(sell_price)         # 输入价格

            pyautogui.move(0, 160, duration=0.5)    # 定位到成交额输入框
            pyautogui.doubleClick()                 # 双击鼠标
            pyautogui.typewrite(sell_amount)        # 输入成交额数量

            # 点击卖出按钮
            pyautogui.click(sell_button_location)

            global TRADE_FLAG
            TRADE_FLAG = 'To_buy'
            log_info("卖出操作完成，交易标志已更新为: To_buy")

        else:
            log_error("卖出按钮未找到")

    except Exception as e:
        log_error(f"卖出操作过程中发生错误: {e}")

    try:
        app = Application(backend='uia').connect(title_re='.*Studio.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)


# ----------------- 主程序 --------------------

def main():
    """主程序 - 仅行情监控和策略分析"""
    # 验证API密钥是否有效 - 这个检查仍然保留，以防API密钥在运行过程中被修改
    if not API_KEY or not API_SECRET or API_KEY == 'placeholder_api_key' or API_SECRET == 'placeholder_api_secret':
        log_error("API密钥未设置或是占位符，请在.env文件中设置有效的API密钥")
        return

    # 检查客户端是否初始化成功
    if client is None:
        log_error("Binance客户端初始化失败，无法继续")
        return

    try:
        # 获取当前价格
        current_price = get_current_price()
        if not current_price:
            log_error("无法获取当前价格，程序退出")
            return

        # 只在控制台显示当前价格，不记录到日志
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 当前{SYMBOL}价格: {current_price}")

        # 获取不同时间周期的K线数据
        intervals = ['1m', '5m', '3m', '15m', '30m']  # 增加1分钟和5分钟周期进行分析
        # 为不同周期设置不同的K线数量
        interval_limits = {
            '1m': 500,   # 1分钟周期获取500根K线
            '5m': 400,   # 5分钟周期获取400根K线
            '3m': 300,   # 3分钟周期获取300根K线
            '15m': 250,  # 15分钟周期获取250根K线
            '30m': 200   # 30分钟周期获取200根K线
        }
        analysis_results = {}

        for interval in intervals:
            # 获取K线数据，使用为每个周期定义的限制
            limit = interval_limits.get(interval, 100)
            klines_df = get_klines(interval=interval, limit=limit)
            if klines_df is None or klines_df.empty:
                log_error(f"无法获取{interval}周期的K线数据")
                continue

            # 检查数据量是否足够
            if len(klines_df) < 60:  # 确保至少有60根K线
                log_info(f"{interval}周期数据量不足，实际获取到 {len(klines_df)} 根K线")
                # 尝试获取更多历史数据
                try:
                    # 根据周期设置不同的历史数据范围
                    if interval == '1m':
                        start_str = "12 hours ago UTC"
                    elif interval == '5m':
                        start_str = "18 hours ago UTC"
                    elif interval == '3m':
                        start_str = "1 day ago UTC"
                    elif interval == '15m':
                        start_str = "3 days ago UTC"
                    else:  # 30m
                        start_str = "5 days ago UTC"

                    log_info(f"尝试获取更多的{interval}周期数据...")
                    more_klines = client.get_historical_klines(
                        symbol=SYMBOL,
                        interval=interval,
                        start_str=start_str,
                        limit=1000  # 增加限制
                    )
                    if more_klines:
                        # 转换为DataFrame
                        more_df = pd.DataFrame(more_klines, columns=[
                            'timestamp', 'open', 'high', 'low', 'close', 'volume',
                            'close_time', 'quote_asset_volume', 'number_of_trades',
                            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
                        ])

                        # 转换数据类型
                        more_df['timestamp'] = pd.to_datetime(more_df['timestamp'], unit='ms')
                        more_df['open'] = more_df['open'].astype(float)
                        more_df['high'] = more_df['high'].astype(float)
                        more_df['low'] = more_df['low'].astype(float)
                        more_df['close'] = more_df['close'].astype(float)
                        more_df['volume'] = more_df['volume'].astype(float)

                        klines_df = more_df
                        log_info(f"成功获取到 {len(klines_df)} 根{interval}周期K线")
                except Exception as e:
                    log_error(f"获取更多{interval}周期数据失败: {e}")

            # 准备数据
            strategy_df = prepare_data_for_strategy(klines_df)
            if strategy_df is None:
                log_error(f"无法准备{interval}周期的策略数据")
                continue

            # 使用高级交易策略分析市场
            result = analyze_advanced_strategy(strategy_df)
            analysis_results[interval] = result

        # 综合分析不同时间周期的结果
        if not analysis_results:
            log_error("没有足够的分析结果，无法生成交易信号")
            return

        # 使用多周期加权分析
        final_recommendation = analyze_multi_timeframe(analysis_results)

        # 生成综合建议并执行交易逻辑
        if final_recommendation == 'BUY' and  TRADE_FLAG == 'To_buy':
            log_info("综合分析: ★★★★ 买入信号 ★★★★")

            buy_price = str(current_price * BUY_DISCOUNT_PERCENT)
            buy_amount = str(TRADE_QTY)
            log_info(f"准备买入: 价格={buy_price}, 金额={buy_amount}")
            buy_crypto(buy_price, buy_amount)

        elif final_recommendation == 'BUY' and TRADE_FLAG == 'To_sell':
            log_info("综合分析: ★★★★ 买入信号 ★★★★ (已下单买入，忽略)")

        elif final_recommendation == 'SELL' and TRADE_FLAG == 'To_sell':
            log_info("综合分析: ☆☆☆☆ 卖出信号 ☆☆☆☆")

            sell_price = str(current_price)
            sell_amount = str(TRADE_QTY)
            log_info(f"准备卖出: 价格={sell_price}, 金额={sell_amount}")
            sell_crypto(sell_price, sell_amount)

        elif final_recommendation == 'SELL' and TRADE_FLAG == 'To_buy':
            log_info("综合分析: ☆☆☆☆ 卖出信号 ☆☆☆☆ (无持仓，忽略)")

        else:
            # 观望信号 - 只在控制台显示，不记录到日志文件
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 综合分析: 观望信号，不进行交易")

        # 输出市场分析（美化格式）- 始终在控制台显示
        print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 市场分析:")

        # 按照时间周期排序（1m, 5m, 3m, 15m, 30m）
        sorted_intervals = sorted(analysis_results.keys(),
                                 key=lambda x: {'1m': 1, '5m': 2, '3m': 3, '15m': 4, '30m': 5}.get(x, 10))

        for interval in sorted_intervals:
            result = analysis_results[interval]

            # 获取市场环境
            market_env = result.get('market_env', 'unknown')

            # 获取指标数据
            indicators = result.get('indicators', {})

            # 获取建议
            recommendation = result.get('recommendation', 'HOLD')

            # 设置市场环境描述
            if market_env == 'trend':
                env_desc = "趋势"
            elif market_env == 'range':
                env_desc = "震荡"
            elif market_env == 'volatile':
                env_desc = "多变"
            else:
                env_desc = "未知"

            # 获取趋势线值和状态
            trend_status = ""
            trend_value = indicators.get('trend_line', None)
            if trend_value is not None and not pd.isna(trend_value):
                if trend_value < 20:
                    trend_status = "超卖"
                elif trend_value > 80:
                    trend_status = "超买"
                else:
                    trend_status = "中性"
                trend_str = f"趋势线:{trend_value:.2f}({trend_status})"
            else:
                trend_str = "趋势线:无数据"

            # 获取RSI值
            rsi_value = indicators.get('rsi', None)
            if rsi_value is not None and not pd.isna(rsi_value):
                rsi_str = f"RSI:{rsi_value:.2f}"
            else:
                rsi_str = "RSI:无数据"

            # 构建美化的输出行
            output_line = f"{interval}: 环境:{env_desc} | {trend_str} | {rsi_str} | 建议:{recommendation}"

            # 始终在控制台显示
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {output_line}")

        # 只有在买入或卖出信号时才记录到日志文件
        if final_recommendation in ['BUY', 'SELL']:
            log_info(f"当前价格: {current_price}")
            log_info("市场分析:")
            for interval in sorted_intervals:
                result = analysis_results[interval]
                market_env = result.get('market_env', 'unknown')
                indicators = result.get('indicators', {})
                recommendation = result.get('recommendation', 'HOLD')

                if market_env == 'trend':
                    env_desc = "趋势"
                elif market_env == 'range':
                    env_desc = "震荡"
                elif market_env == 'volatile':
                    env_desc = "多变"
                else:
                    env_desc = "未知"

                trend_value = indicators.get('trend_line', None)
                if trend_value is not None and not pd.isna(trend_value):
                    if trend_value < 20:
                        trend_status = "超卖"
                    elif trend_value > 80:
                        trend_status = "超买"
                    else:
                        trend_status = "中性"
                    trend_str = f"趋势线:{trend_value:.2f}({trend_status})"
                else:
                    trend_str = "趋势线:无数据"

                rsi_value = indicators.get('rsi', None)
                if rsi_value is not None and not pd.isna(rsi_value):
                    rsi_str = f"RSI:{rsi_value:.2f}"
                else:
                    rsi_str = "RSI:无数据"

                output_line = f"{interval}: 环境:{env_desc} | {trend_str} | {rsi_str} | 建议:{recommendation}"
                log_info(output_line)

    except Exception as e:
        error_msg = f"程序执行异常: {e}"
        log_error(error_msg)
        # 记录详细的错误堆栈
        error_traceback = traceback.format_exc()
        log_error(f"错误详情:\n{error_traceback}")

if __name__ == "__main__":
    print("行情监控程序开始执行...")

    # 记录启动信息到日志文件
    with open('trading_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()} - 行情监控程序开始执行...\n")

    # 打印固定参数信息（只打印一次）
    log_info("交易监控程序启动...")
    log_info(f"监控交易对: {SYMBOL}")
    log_info(f"买入折扣百分比: {BUY_DISCOUNT_PERCENT*100:.1f}%")
    log_info(f"交易金额: {TRADE_QTY} USDT")
    log_info(f"当前交易标志: {TRADE_FLAG}")
    log_info("注意: 程序会在检测到买入信号时自动执行买入操作")

    # 测试API连接（只测试一次）
    log_info("测试API连接...")
    try:
        client.ping()
        log_info("API连接正常")
    except Exception as e:
        log_error(f"API连接失败: {e}")
        exit(1)  # 如果API连接失败，直接退出程序

    # 运行计数器
    run_count = 0

    try:
        while True:
            run_count += 1

            # 只在控制台显示分析次数，不记录到日志
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 第 {run_count} 次分析 (交易标志: {TRADE_FLAG})")

            # 执行主程序
            main()

            # 等待下次分析
            print("=" * 40)
            time.sleep(2)

    except KeyboardInterrupt:
        log_info("程序被用户中断")
    except Exception as e:
        log_error(f"程序运行异常: {e}")
        # 记录详细的错误堆栈
        error_traceback = traceback.format_exc()
        log_error(f"错误详情:\n{error_traceback}")
    finally:
        log_info("程序结束")
