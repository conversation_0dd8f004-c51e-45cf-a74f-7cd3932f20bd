2025-05-29 12:53:28.720858 - 行情监控程序开始执行...
2025-05-29 12:53:28,766 - INFO - 交易监控程序启动...
2025-05-29 12:53:28,774 - INFO - 监控交易对: ARBUSDT
2025-05-29 12:53:28,774 - INFO - 买入折扣百分比: 19.8%
2025-05-29 12:53:28,777 - INFO - 交易金额: 10.0 USDT
2025-05-29 12:53:28,778 - INFO - 当前交易标志: sell
2025-05-29 12:53:28,786 - INFO - 注意: 程序会在检测到买入信号时自动执行买入操作
2025-05-29 12:53:28,786 - INFO - 测试API连接...
2025-05-29 12:53:28,919 - INFO - API连接正常
2025-05-29 12:53:28,920 - INFO - 第 1 次分析 (交易标志: sell)
2025-05-29 12:53:29,045 - INFO - 当前ARBUSDT价格: 0.4257
2025-05-29 12:53:29,929 - INFO - 综合分析: ★★★★ 买入信号 ★★★★
2025-05-29 12:53:33,576 - ERROR - 程序执行异常: 'float' object is not iterable
2025-05-29 12:53:33,579 - ERROR - 错误详情:
Traceback (most recent call last):
  File "d:\ccp\nature\binance\auto_trade_binan_http.py", line 460, in main
    buy_crypto(buy_price, buy_amount)
  File "d:\ccp\nature\binance\auto_trade_binan_http.py", line 295, in buy_crypto
    pyautogui.typewrite(buy_amount)         # 输入成交额数量
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\Python311\Lib\site-packages\pyautogui\__init__.py", line 594, in wrapper
    returnVal = wrappedFunction(*args, **kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ProgramFiles\Python311\Lib\site-packages\pyautogui\__init__.py", line 1682, in typewrite
    for c in message:
TypeError: 'float' object is not iterable

2025-05-29 12:53:33,580 - INFO - 等待下次分析...
2025-05-29 12:53:35,581 - INFO - 第 2 次分析 (交易标志: sell)
2025-05-29 12:53:35,700 - INFO - 当前ARBUSDT价格: 0.4258
2025-05-29 12:53:36,692 - INFO - 综合分析: ★★★★ 买入信号 ★★★★
2025-05-29 12:53:40,185 - INFO - 程序被用户中断
2025-05-29 12:53:40,185 - INFO - 程序结束
