#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试WebSocket连接功能
"""

import websocket
import json
import threading
import time
from datetime import datetime

def on_message(ws, message):
    """处理WebSocket消息"""
    try:
        data = json.loads(message)
        if 'stream' in data:
            stream_name = data['stream']
            stream_data = data['data']
            
            if '@kline_' in stream_name:
                # K线数据
                kline = stream_data['k']
                if kline['x']:  # 只处理完成的K线
                    print(f"{datetime.now().strftime('%H:%M:%S')} - {stream_name}: 价格={kline['c']}, 成交量={kline['v']}")
            elif '@ticker' in stream_name:
                # 价格数据
                print(f"{datetime.now().strftime('%H:%M:%S')} - 价格更新: {stream_data['c']}")
    except Exception as e:
        print(f"处理消息失败: {e}")

def on_error(ws, error):
    """WebSocket错误处理"""
    print(f"WebSocket错误: {error}")

def on_close(ws, close_status_code, close_msg):
    """WebSocket关闭处理"""
    print("WebSocket连接已关闭")

def on_open(ws):
    """WebSocket连接打开"""
    print("WebSocket连接已建立")

def test_websocket():
    """测试WebSocket连接"""
    symbol = "arbusdt"
    
    # K线数据流
    kline_streams = [f"{symbol}@kline_1m", f"{symbol}@kline_5m"]
    
    # 价格数据流
    ticker_stream = f"{symbol}@ticker"
    
    # 组合流URL
    streams = kline_streams + [ticker_stream]
    stream_url = f"wss://stream.binance.com:9443/ws/{'/'.join(streams)}"
    
    print(f"连接URL: {stream_url}")
    
    # 创建WebSocket连接
    ws = websocket.WebSocketApp(stream_url,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close,
                                on_open=on_open)
    
    # 在单独线程中运行
    def run_ws():
        ws.run_forever()
    
    ws_thread = threading.Thread(target=run_ws, daemon=True)
    ws_thread.start()
    
    return ws

if __name__ == "__main__":
    print("=" * 60)
    print("WebSocket连接测试")
    print("=" * 60)
    
    try:
        ws = test_websocket()
        
        # 运行30秒
        print("运行30秒，观察数据...")
        time.sleep(30)
        
        print("测试完成")
        
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")
