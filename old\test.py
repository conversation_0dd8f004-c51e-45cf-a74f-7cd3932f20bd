
import time
import hmac
import hashlib
import requests
import json

# 将输出写入文件
output_file = 'order_results.txt'
with open(output_file, 'w', encoding='utf-8') as f:
    f.write("开始处理币安API下单请求...\n\n")

    try:
        # 设置身份验证：
        API_KEY = "amwoAuf70aNaOOD2Y6WYgGuLqcUwg47spr9ojoIL1RKcN6qsagCyPuZnyUDayBGh"
        PRIVATE_KEY = "zfSAEduiBAZ1K7USZpHq4K91AXtBkQPPoBO5BK7JF1C7qM77Rh1rUm0YrQObDwuY"

        # 首先获取当前BTC价格
        f.write("正在获取当前BTC价格...\n")
        price_url = "https://testnet.binance.vision/api/v3/ticker/price?symbol=BTCUSDT"
        price_response = requests.get(price_url, timeout=10)

        if price_response.status_code == 200:
            price_data = json.loads(price_response.text)
            current_price = float(price_data["price"])
            f.write(f"当前BTC价格: {current_price} USDT\n\n")

            # 设置略低于市场价格的卖单（比如当前价格的99%）
            sell_price = round(current_price * 0.99, 2)

            # 设置请求：
            API_METHOD = "POST"
            API_CALL = "api/v3/order"
            API_PARAMS = f"symbol=BTCUSDT&side=SELL&type=LIMIT&timeInForce=GTC&quantity=0.001&price={sell_price}"

            # 签署请求：
            timestamp = int(time.time() * 1000)  # 获取当前时间戳（毫秒）
            api_params_with_timestamp = f"{API_PARAMS}&timestamp={timestamp}"

            # 使用HMAC SHA256生成签名
            signature = hmac.new(
                PRIVATE_KEY.encode('utf-8'),
                api_params_with_timestamp.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 发送请求：
            url = f"https://testnet.binance.vision/{API_CALL}"
            headers = {
                "X-MBX-APIKEY": API_KEY
            }
            params = f"{api_params_with_timestamp}&signature={signature}"

            # 记录请求信息
            f.write(f"请求URL: {url}?{params}\n")
            f.write(f"请求头: {headers}\n\n")

            # 发送请求
            f.write("正在发送下单请求...\n")
            response = requests.post(f"{url}?{params}", headers=headers, timeout=30)
            f.write(f"响应状态码: {response.status_code}\n")
            f.write(f"响应内容: {response.text}\n")
        else:
            f.write(f"获取价格失败，状态码: {price_response.status_code}\n")
            f.write(f"错误信息: {price_response.text}\n")

    except Exception as e:
        f.write(f"处理过程中出错: {e}\n")

    f.write("\n处理完成\n")

print(f"处理完成，结果已写入 {output_file}")