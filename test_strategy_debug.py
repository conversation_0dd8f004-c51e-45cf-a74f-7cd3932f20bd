#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试高级交易策略（带调试）
"""

import pandas as pd
import numpy as np
import traceback

# 创建测试数据
def create_test_data(n=100):
    dates = pd.date_range(start='2023-01-01', periods=n, freq='D')
    data = {
        'open': np.random.normal(100, 5, n),
        'high': np.random.normal(105, 5, n),
        'low': np.random.normal(95, 5, n),
        'close': np.random.normal(100, 5, n),
        'volume': np.random.normal(1000, 200, n)
    }
    
    # 确保high > low
    for i in range(n):
        data['high'][i] = max(data['high'][i], data['low'][i] + 1)
    
    df = pd.DataFrame(data, index=dates)
    return df

# 测试策略
def test_strategy():
    try:
        print("创建测试数据...")
        df = create_test_data()
        
        print("尝试导入策略模块...")
        try:
            from Trading_Strategy_Advanced import AdvancedTradingStrategy, analyze_multi_timeframe
            print("成功导入策略模块")
        except Exception as e:
            print(f"导入策略模块失败: {e}")
            print(traceback.format_exc())
            return
        
        print("初始化策略...")
        try:
            strategy = AdvancedTradingStrategy(df)
            print("成功初始化策略")
        except Exception as e:
            print(f"初始化策略失败: {e}")
            print(traceback.format_exc())
            return
        
        print("获取市场状态...")
        try:
            status = strategy.get_market_status()
            print("成功获取市场状态")
        except Exception as e:
            print(f"获取市场状态失败: {e}")
            print(traceback.format_exc())
            return
        
        print("市场环境:", status['market_env'])
        print("建议:", status['recommendation'])
        print("信号数量:", len(status['signals']))
        
        if 'indicators' in status:
            print("指标:")
            for key, value in status['indicators'].items():
                print(f"  {key}: {value}")
        
        # 测试多周期分析
        print("\n测试多周期分析...")
        try:
            results = {
                '30m': {'recommendation': 'BUY'},
                '1h': {'recommendation': 'HOLD'},
                '1d': {'recommendation': 'SELL'}
            }
            
            final_rec = analyze_multi_timeframe(results)
            print("多周期分析结果:", final_rec)
        except Exception as e:
            print(f"多周期分析失败: {e}")
            print(traceback.format_exc())
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        print(traceback.format_exc())

if __name__ == "__main__":
    print("开始测试高级交易策略...")
    test_strategy()
    print("测试完成")
