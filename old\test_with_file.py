import requests
import time

# 将输出写入文件
with open('api_test_results.txt', 'w', encoding='utf-8') as f:
    f.write("开始测试币安API连接...\n")
    
    try:
        # 测试公共API端点（不需要签名）
        url = "https://testnet.binance.vision/api/v3/ping"
        f.write(f"正在请求: {url}\n")
        
        response = requests.get(url, timeout=10)
        f.write(f"响应状态码: {response.status_code}\n")
        f.write(f"响应内容: {response.text}\n")
        
        # 获取服务器时间
        time_url = "https://testnet.binance.vision/api/v3/time"
        f.write(f"\n正在请求服务器时间: {time_url}\n")
        
        time_response = requests.get(time_url, timeout=10)
        f.write(f"响应状态码: {time_response.status_code}\n")
        f.write(f"响应内容: {time_response.text}\n")
        
        # 获取交易对信息
        symbols_url = "https://testnet.binance.vision/api/v3/ticker/price?symbol=BTCUSDT"
        f.write(f"\n正在获取BTCUSDT价格: {symbols_url}\n")
        
        symbols_response = requests.get(symbols_url, timeout=10)
        f.write(f"响应状态码: {symbols_response.status_code}\n")
        f.write(f"响应内容: {symbols_response.text}\n")
        
    except Exception as e:
        f.write(f"测试过程中出错: {e}\n")
    
    f.write("\n测试完成\n")
