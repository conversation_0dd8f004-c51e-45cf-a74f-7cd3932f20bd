# 模拟鼠标和键盘输入
import pyautogui
import time
from pywinauto.application import Application

# 以下是在网站买入的函数    
def buy_crypto(buy_price, buy_amount):
    # 尝试连接谷歌浏览器窗口
    try:
        app = Application(backend='uia').connect(title_re='.*Chrome.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)

    # 移动鼠标到屏幕中点
    pyautogui.moveTo(960, 540)

    time.sleep(0.5)

    # 根据买入按钮图片定位
    buy_button_location = pyautogui.locateOnScreen(r'img\buy.png')

    if buy_button_location is not None:

        pyautogui.moveTo(buy_button_location)   # 定位到买入按钮
        
        pyautogui.move(0, -570, duration=0.5)   # 定位到价格输入框
        time.sleep(1)
        pyautogui.doubleClick()                 # 双击鼠标
        pyautogui.typewrite(buy_price)           # 输入0.044

        pyautogui.move(0, 160, duration=0.5)    # 定位到成交额输入框
        pyautogui.doubleClick()                 # 双击鼠标
        pyautogui.typewrite(buy_amount)         # 输入成交额数量
        
        # # 点击买入按钮
        # pyautogui.click(buy_button_location)
        
    else:
        print("买入按钮未找到")

    try:
        app = Application(backend='uia').connect(title_re='.*Studio.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)

# 以下是在网站卖出的函数
def sell_crypto(sell_price, sell_amount):
    # 尝试连接谷歌浏览器窗口
    try:
        app = Application(backend='uia').connect(title_re='.*Chrome.*')
        window = app.top_window()
        window.set_focus()
    except Exception as e:
        print("找不到Chrome窗口:", e)

    # 移动鼠标到屏幕中点
    pyautogui.moveTo(960, 540)
    
    time.sleep(0.5)

    # 根据卖出按钮图片定位
    sell_button_location = pyautogui.locateOnScreen(r'img\sell.png')

    if sell_button_location is not None:

        pyautogui.moveTo(sell_button_location)   # 定位到卖出按钮
        
        pyautogui.move(0, -570, duration=0.5)   # 定位到价格输入框
        time.sleep(1)
        pyautogui.doubleClick()                 # 双击鼠标
        pyautogui.typewrite(sell_price)         # 输入0.044

        pyautogui.move(0, 160, duration=0.5)    # 定位到成交额输入框
        pyautogui.doubleClick()                 # 双击鼠标
        pyautogui.typewrite(sell_amount)        # 输入成交额数量
        
        # # 点击买入按钮
        # pyautogui.click(sell_button_location)

    else:
        print("卖出按钮未找到")
    pyautogui.hotkey('alt', 'tab')


buy_price = '0.0344'
buy_amount = '10'
buy_crypto(buy_price, buy_amount)

sell_price = '0.654'
sell_amount = '10'
# sell_crypto(sell_price, sell_amount)



