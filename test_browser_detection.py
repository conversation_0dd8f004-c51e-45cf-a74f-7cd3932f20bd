#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试浏览器窗口检测功能
"""

import time
from pywinauto import Desktop

def list_all_windows():
    """列出所有打开的窗口"""
    try:
        desktop = Desktop(backend="uia")
        windows = desktop.windows()

        print("所有打开的窗口:")
        for i, window in enumerate(windows, 1):
            try:
                title = window.window_text()
                if title.strip():  # 只显示有标题的窗口
                    print(f"  {i}. {title}")
            except:
                continue

    except Exception as e:
        print(f"列出窗口时发生错误: {e}")

def list_browser_windows():
    """列出当前打开的浏览器窗口"""
    try:
        desktop = Desktop(backend="uia")
        windows = desktop.windows()

        browser_windows = []
        for window in windows:
            try:
                title = window.window_text()
                if any(browser in title.lower() for browser in ['chrome', 'edge', 'firefox', 'browser', '浏览器']):
                    browser_windows.append(title)
            except:
                continue

        if browser_windows:
            print("找到以下浏览器窗口:")
            for i, title in enumerate(browser_windows, 1):
                print(f"  {i}. {title}")
        else:
            print("未找到任何浏览器窗口")

        return browser_windows
    except Exception as e:
        print(f"列出浏览器窗口时发生错误: {e}")
        return []

def test_browser_connection():
    """测试浏览器连接（使用新方法）"""
    print("测试浏览器连接...")

    try:
        desktop = Desktop(backend="uia")
        windows = desktop.windows()

        browser_window = None
        for window in windows:
            try:
                title = window.window_text()
                if any(browser in title.lower() for browser in ['chrome', 'edge', 'firefox', 'browser', '浏览器']):
                    browser_window = window
                    print(f"✅ 找到浏览器窗口: {title}")
                    break
            except:
                continue

        if browser_window:
            try:
                browser_window.set_focus()
                print("✅ 成功激活浏览器窗口")
                return True
            except Exception as e:
                print(f"❌ 激活浏览器窗口失败: {e}")
                return False
        else:
            print("❌ 找不到任何浏览器窗口")
            return False

    except Exception as e:
        print(f"❌ 浏览器连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("浏览器窗口检测测试")
    print("=" * 60)

    print("\n1. 列出所有窗口:")
    list_all_windows()

    print("\n2. 列出浏览器窗口:")
    list_browser_windows()

    print("\n3. 测试浏览器连接:")
    success = test_browser_connection()

    if not success:
        print("\n❌ 无法连接到任何浏览器窗口")
        print("请确保：")
        print("  1. 浏览器已打开")
        print("  2. 浏览器窗口可见（未最小化）")
        print("  3. 浏览器窗口标题包含 'Chrome'、'Edge' 或 'Firefox'")
    else:
        print("\n✅ 浏览器连接测试成功")

    print("\n=" * 60)
    print("测试完成")
    print("=" * 60)
