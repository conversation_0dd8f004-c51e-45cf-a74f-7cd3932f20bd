#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级交易策略模块
结合华尔街流行的技术分析方法，提供更全面的市场分析和交易信号生成

功能：
1. 多指标分析：结合移动平均线、MACD、RSI、布林带等指标
2. 成交量分析：考虑成交量确认价格走势
3. 市场环境自适应：根据不同市场环境调整策略
4. 多周期加权分析：不同时间周期的信号加权处理
5. 改进的风险管理：基于ATR的止损设置和仓位管理
"""

import pandas as pd
import numpy as np
import math
import logging

class AdvancedTradingStrategy:
    """高级交易策略类"""

    def __init__(self, data, risk_pct=2.0):
        """
        初始化高级交易策略

        参数:
            data: DataFrame, 包含OHLC数据
            risk_pct: float, 风险百分比（相对于账户总额）
        """
        self.data = data.copy() if data is not None else None
        self.risk_pct = risk_pct

        # 初始化市场环境
        self.market_env = 'unknown'

        # 检查数据有效性
        if self.data is not None and not self.data.empty:
            # 计算所有技术指标
            self._calculate_all_indicators()

            # 判断市场环境
            self.market_env = self._determine_market_environment()

    def _calculate_all_indicators(self):
        """计算所有技术指标"""
        # 移动平均线
        self._calculate_moving_averages()

        # RSI
        self._calculate_rsi()

        # MACD
        self._calculate_macd()

        # 布林带
        self._calculate_bollinger_bands()

        # ATR
        self._calculate_atr()

        # 趋势线指标 (基于通达信策略)
        self._calculate_trend_line()

        # 成交量指标
        self._calculate_volume_indicators()

    def _calculate_moving_averages(self, fast_period=10, slow_period=20, long_period=50):
        """
        计算移动平均线 (调整为更短的周期，适合短线交易)

        参数:
            fast_period: int, 快速均线周期 (10)
            slow_period: int, 慢速均线周期 (20)
            long_period: int, 长期均线周期 (50)
        """
        # 确保数据量足够
        if len(self.data) < long_period:
            # 如果数据不足，调整周期
            long_period = min(long_period, max(10, len(self.data) // 2))
            slow_period = min(slow_period, max(5, len(self.data) // 4))
            fast_period = min(fast_period, max(3, len(self.data) // 8))

        # 计算移动平均线
        self.data['sma_fast'] = self.data['close'].rolling(window=fast_period, min_periods=1).mean()
        self.data['sma_slow'] = self.data['close'].rolling(window=slow_period, min_periods=1).mean()
        self.data['sma_long'] = self.data['close'].rolling(window=long_period, min_periods=1).mean()

        # 计算均线交叉信号
        self.data['ma_cross_up'] = (self.data['sma_fast'] > self.data['sma_slow']) & (self.data['sma_fast'].shift(1) <= self.data['sma_slow'].shift(1))
        self.data['ma_cross_down'] = (self.data['sma_fast'] < self.data['sma_slow']) & (self.data['sma_fast'].shift(1) >= self.data['sma_slow'].shift(1))

    def _calculate_rsi(self, period=9):
        """
        计算RSI指标 (调整为更短的周期，适合短线交易)

        参数:
            period: int, RSI计算周期 (9)
        """
        # 确保数据量足够
        if len(self.data) < period:
            period = max(2, len(self.data) // 2)

        # 计算价格变化
        delta = self.data['close'].diff()

        # 分离上涨和下跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # 计算平均上涨和下跌
        avg_gain = gain.rolling(window=period, min_periods=1).mean()
        avg_loss = loss.rolling(window=period, min_periods=1).mean()

        # 计算相对强度
        rs = avg_gain / avg_loss.replace(0, 0.001)  # 避免除零错误

        # 计算RSI
        self.data['rsi'] = 100 - (100 / (1 + rs))

        # 填充NaN值
        self.data['rsi'] = self.data['rsi'].fillna(50)

        # 计算超买超卖信号
        self.data['oversold'] = self.data['rsi'] < 30
        self.data['overbought'] = self.data['rsi'] > 70

    def _calculate_macd(self, fast_period=6, slow_period=13, signal_period=4):
        """
        计算MACD指标 (调整为更短的周期，适合短线交易)

        参数:
            fast_period: int, 快线周期 (6)
            slow_period: int, 慢线周期 (13)
            signal_period: int, 信号线周期 (4)
        """
        # 确保数据量足够
        if len(self.data) < slow_period + signal_period:
            # 如果数据不足，调整周期
            slow_period = min(slow_period, max(5, len(self.data) // 3))
            fast_period = min(fast_period, max(3, len(self.data) // 6))
            signal_period = min(signal_period, max(2, len(self.data) // 9))

        # 计算EMA
        ema_fast = self.data['close'].ewm(span=fast_period, min_periods=1, adjust=False).mean()
        ema_slow = self.data['close'].ewm(span=slow_period, min_periods=1, adjust=False).mean()

        # 计算MACD线和信号线
        self.data['macd'] = ema_fast - ema_slow
        self.data['macd_signal'] = self.data['macd'].ewm(span=signal_period, min_periods=1, adjust=False).mean()
        self.data['macd_hist'] = self.data['macd'] - self.data['macd_signal']

        # 计算MACD交叉信号
        self.data['macd_cross_up'] = (self.data['macd'] > self.data['macd_signal']) & (self.data['macd'].shift(1) <= self.data['macd_signal'].shift(1))
        self.data['macd_cross_down'] = (self.data['macd'] < self.data['macd_signal']) & (self.data['macd'].shift(1) >= self.data['macd_signal'].shift(1))

    def _calculate_bollinger_bands(self, period=15, std_dev=2):
        """
        计算布林带指标 (调整为更短的周期，适合短线交易)

        参数:
            period: int, 布林带周期 (15)
            std_dev: float, 标准差倍数 (2)
        """
        # 确保数据量足够
        if len(self.data) < period:
            period = max(2, len(self.data) // 2)

        # 计算中轨（简单移动平均线）
        self.data['bb_middle'] = self.data['close'].rolling(window=period, min_periods=1).mean()

        # 计算标准差
        rolling_std = self.data['close'].rolling(window=period, min_periods=1).std()

        # 计算上轨和下轨
        self.data['bb_upper'] = self.data['bb_middle'] + (rolling_std * std_dev)
        self.data['bb_lower'] = self.data['bb_middle'] - (rolling_std * std_dev)

        # 计算带宽
        self.data['bb_width'] = (self.data['bb_upper'] - self.data['bb_lower']) / self.data['bb_middle']

        # 计算布林带信号
        self.data['bb_upper_break'] = self.data['close'] > self.data['bb_upper']
        self.data['bb_lower_break'] = self.data['close'] < self.data['bb_lower']

    def _calculate_atr(self, period=7):
        """
        计算ATR指标 (调整为更短的周期，适合短线交易)

        参数:
            period: int, ATR计算周期 (7)
        """
        # 确保数据量足够
        if len(self.data) < period:
            period = max(2, len(self.data) // 2)

        # 计算真实波幅（TR）
        high_low = self.data['high'] - self.data['low']
        high_close = abs(self.data['high'] - self.data['close'].shift(1))
        low_close = abs(self.data['low'] - self.data['close'].shift(1))

        tr = pd.DataFrame({'hl': high_low, 'hc': high_close, 'lc': low_close}).max(axis=1)

        # 计算ATR
        self.data['atr'] = tr.rolling(window=period, min_periods=1).mean()

    def _calculate_trend_line(self, n=20, m1=3, m2=2, m3=2):
        """
        计算趋势线指标 (基于通达信策略，调整为更短的周期，适合短线交易)

        参数:
            n: int, 周期 (20)
            m1: int, SMA周期1 (3)
            m2: int, SMA周期2 (2)
            m3: int, EMA周期 (2)
        """
        # 确保数据量足够
        if len(self.data) < n + m1 + m2 + m3:
            # 如果数据不足，调整周期
            n = min(n, max(10, len(self.data) // 3))
            m1 = min(m1, max(2, len(self.data) // 10))
            m2 = min(m2, max(1, len(self.data) // 20))
            m3 = min(m3, max(1, len(self.data) // 20))

        # 计算最低价的n周期最低值
        low_min = self.data['low'].rolling(window=n, min_periods=1).min()

        # 计算最高价的n周期最高值
        high_max = self.data['high'].rolling(window=n, min_periods=1).max()

        # 计算RSV值，处理除零情况
        rsv = np.zeros(len(self.data))
        for i in range(len(self.data)):
            if high_max.iloc[i] - low_min.iloc[i] != 0:
                rsv[i] = (self.data['close'].iloc[i] - low_min.iloc[i]) / (high_max.iloc[i] - low_min.iloc[i]) * 100
            else:
                # 如果最高价等于最低价，RSV设为50
                rsv[i] = 50

        rsv_series = pd.Series(rsv, index=self.data.index)

        # 计算V11 (3*SMA(RSV,5,1)-2*SMA(SMA(RSV,5,1),3,1))
        sma1 = rsv_series.rolling(window=m1, min_periods=1).mean()
        sma2 = sma1.rolling(window=m2, min_periods=1).mean()
        v11 = 3 * sma1 - 2 * sma2

        # 计算趋势线 (EMA(V11,3))
        self.data['trend_line'] = v11.ewm(span=m3, min_periods=1, adjust=False).mean()

        # 填充NaN值
        self.data['trend_line'] = self.data['trend_line'].fillna(50)

        # 计算趋势线信号
        self.data['trend_oversold'] = self.data['trend_line'] < 20
        self.data['trend_overbought'] = self.data['trend_line'] > 80

        # 计算趋势线上穿/下穿信号
        for threshold in [0, 1, 3, 6, 11, 20, 50, 80, 89, 94, 97, 99, 100]:
            self.data[f'trend_cross_up_{threshold}'] = (self.data['trend_line'] > threshold) & (self.data['trend_line'].shift(1) <= threshold)
            self.data[f'trend_cross_down_{threshold}'] = (self.data['trend_line'] < threshold) & (self.data['trend_line'].shift(1) >= threshold)

    def _calculate_volume_indicators(self, period=10):
        """
        计算成交量指标 (调整为更短的周期，适合短线交易)

        参数:
            period: int, 计算周期 (10)
        """
        # 确保数据量足够
        if len(self.data) < period:
            period = max(2, len(self.data) // 2)

        # 检查是否有成交量数据
        if 'volume' not in self.data.columns:
            # 如果没有成交量数据，创建一个虚拟的成交量列
            self.data['volume'] = 1
            return

        # 计算成交量移动平均
        self.data['volume_sma'] = self.data['volume'].rolling(window=period, min_periods=1).mean()

        # 计算成交量相对强度
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_sma']

        # 计算OBV (On-Balance Volume)
        obv = np.zeros(len(self.data))
        obv[0] = self.data['volume'].iloc[0]

        for i in range(1, len(self.data)):
            if self.data['close'].iloc[i] > self.data['close'].iloc[i-1]:
                obv[i] = obv[i-1] + self.data['volume'].iloc[i]
            elif self.data['close'].iloc[i] < self.data['close'].iloc[i-1]:
                obv[i] = obv[i-1] - self.data['volume'].iloc[i]
            else:
                obv[i] = obv[i-1]

        self.data['obv'] = obv

        # 计算OBV移动平均
        self.data['obv_sma'] = self.data['obv'].rolling(window=period, min_periods=1).mean()

        # 计算成交量放大信号
        self.data['volume_surge'] = self.data['volume_ratio'] > 2.0

    def _determine_market_environment(self):
        """
        判断市场环境

        返回:
            str: 市场环境 - "trend"(趋势), "range"(震荡), "volatile"(多变)
        """
        if self.data is None or len(self.data) < 20:
            return 'unknown'

        # 获取最近数据
        recent_data = self.data.iloc[-20:]

        # 计算价格波动率 (基于ATR)
        if 'atr' in recent_data.columns:
            volatility = recent_data['atr'].iloc[-1] / recent_data['close'].iloc[-1] * 100
        else:
            volatility = recent_data['close'].pct_change().abs().mean() * 100

        # 计算布林带宽度
        if 'bb_width' in recent_data.columns:
            bb_width = recent_data['bb_width'].iloc[-1] * 100
        else:
            bb_width = 4  # 默认值

        # 计算趋势强度 (基于移动平均线)
        if 'sma_fast' in recent_data.columns and 'sma_slow' in recent_data.columns:
            ma_diff = abs(recent_data['sma_fast'].iloc[-1] - recent_data['sma_slow'].iloc[-1]) / recent_data['close'].iloc[-1] * 100
        else:
            ma_diff = 1  # 默认值

        # 判断市场环境 (调整参数，使其更适合短周期)
        if ma_diff > 1.5 and volatility > 1.0:  # 强趋势 (降低阈值)
            return "trend"
        elif volatility < 0.8 and bb_width < 2.5:  # 低波动率，窄布林带 (降低阈值)
            return "range"
        else:  # 高波动率或宽布林带
            return "volatile"

    def generate_signals(self):
        """
        生成交易信号

        返回:
            list: 交易信号列表，每个元素为(action, price, size, stop_loss)
        """
        if self.data is None or len(self.data) < 2:
            return []

        # 获取最新数据
        current = self.data.iloc[-1]
        prev = self.data.iloc[-2]

        # 初始化信号列表
        signals = []

        # 根据市场环境选择不同的信号生成策略
        if self.market_env == "trend":
            signals = self._generate_trend_signals(current, prev)
        elif self.market_env == "range":
            signals = self._generate_range_signals(current, prev)
        else:  # volatile or unknown
            signals = self._generate_volatile_signals(current, prev)

        return signals

    def _generate_trend_signals(self, current, prev):
        """
        生成趋势市场的交易信号

        参数:
            current: Series, 当前K线数据
            prev: Series, 前一K线数据

        返回:
            list: 交易信号列表
        """
        signals = []

        # 买入信号条件
        buy_signal = False

        # 1. 移动平均线金叉
        if 'ma_cross_up' in current and current['ma_cross_up']:
            buy_signal = True

        # 2. MACD金叉
        if 'macd_cross_up' in current and current['macd_cross_up']:
            buy_signal = True

        # 3. 趋势线上穿20
        if 'trend_cross_up_20' in current and current['trend_cross_up_20']:
            buy_signal = True

        # 4. 价格突破上轨且成交量放大
        if ('bb_upper_break' in current and current['bb_upper_break'] and
            'volume_surge' in current and current['volume_surge']):
            buy_signal = True

        # 卖出信号条件
        sell_signal = False

        # 1. 移动平均线死叉
        if 'ma_cross_down' in current and current['ma_cross_down']:
            sell_signal = True

        # 2. MACD死叉
        if 'macd_cross_down' in current and current['macd_cross_down']:
            sell_signal = True

        # 3. 趋势线下穿80
        if 'trend_cross_down_80' in current and current['trend_cross_down_80']:
            sell_signal = True

        # 生成信号
        if buy_signal:
            price = current['close']
            stop_loss = self._calculate_stop_loss('buy', price)
            signals.append(('buy', price, 1.0, stop_loss))
        elif sell_signal:
            price = current['close']
            stop_loss = self._calculate_stop_loss('sell', price)
            signals.append(('sell', price, 1.0, stop_loss))

        return signals

    def _generate_range_signals(self, current, prev):
        """
        生成震荡市场的交易信号

        参数:
            current: Series, 当前K线数据
            prev: Series, 前一K线数据

        返回:
            list: 交易信号列表
        """
        signals = []

        # 买入信号条件
        buy_signal = False

        # 1. RSI超卖
        if 'oversold' in current and current['oversold']:
            buy_signal = True

        # 2. 价格触及布林带下轨
        if 'bb_lower_break' in current and current['bb_lower_break']:
            buy_signal = True

        # 3. 趋势线低于20且上升
        if ('trend_line' in current and 'trend_line' in prev and
            current['trend_line'] < 20 and current['trend_line'] > prev['trend_line']):
            buy_signal = True

        # 卖出信号条件
        sell_signal = False

        # 1. RSI超买
        if 'overbought' in current and current['overbought']:
            sell_signal = True

        # 2. 价格触及布林带上轨
        if 'bb_upper_break' in current and current['bb_upper_break']:
            sell_signal = True

        # 3. 趋势线高于80且下降
        if ('trend_line' in current and 'trend_line' in prev and
            current['trend_line'] > 80 and current['trend_line'] < prev['trend_line']):
            sell_signal = True

        # 生成信号
        if buy_signal:
            price = current['close']
            stop_loss = self._calculate_stop_loss('buy', price)
            signals.append(('buy', price, 1.0, stop_loss))
        elif sell_signal:
            price = current['close']
            stop_loss = self._calculate_stop_loss('sell', price)
            signals.append(('sell', price, 1.0, stop_loss))

        return signals

    def _generate_volatile_signals(self, current, prev):
        """
        生成多变市场的交易信号

        参数:
            current: Series, 当前K线数据
            prev: Series, 前一K线数据

        返回:
            list: 交易信号列表
        """
        signals = []

        # 在多变市场中，我们需要更强的确认信号

        # 买入信号条件 (需要多个指标确认)
        buy_signal = False
        buy_confirmations = 0

        # 1. 移动平均线金叉
        if 'ma_cross_up' in current and current['ma_cross_up']:
            buy_confirmations += 1

        # 2. MACD金叉
        if 'macd_cross_up' in current and current['macd_cross_up']:
            buy_confirmations += 1

        # 3. RSI超卖
        if 'oversold' in current and current['oversold']:
            buy_confirmations += 1

        # 4. 趋势线上穿20
        if 'trend_cross_up_20' in current and current['trend_cross_up_20']:
            buy_confirmations += 1

        # 5. 成交量放大
        if 'volume_surge' in current and current['volume_surge']:
            buy_confirmations += 1

        # 需要至少3个确认
        if buy_confirmations >= 3:
            buy_signal = True

        # 卖出信号条件 (需要多个指标确认)
        sell_signal = False
        sell_confirmations = 0

        # 1. 移动平均线死叉
        if 'ma_cross_down' in current and current['ma_cross_down']:
            sell_confirmations += 1

        # 2. MACD死叉
        if 'macd_cross_down' in current and current['macd_cross_down']:
            sell_confirmations += 1

        # 3. RSI超买
        if 'overbought' in current and current['overbought']:
            sell_confirmations += 1

        # 4. 趋势线下穿80
        if 'trend_cross_down_80' in current and current['trend_cross_down_80']:
            sell_confirmations += 1

        # 5. 成交量放大
        if 'volume_surge' in current and current['volume_surge']:
            sell_confirmations += 1

        # 需要至少3个确认
        if sell_confirmations >= 3:
            sell_signal = True

        # 生成信号
        if buy_signal:
            price = current['close']
            stop_loss = self._calculate_stop_loss('buy', price)
            signals.append(('buy', price, 1.0, stop_loss))
        elif sell_signal:
            price = current['close']
            stop_loss = self._calculate_stop_loss('sell', price)
            signals.append(('sell', price, 1.0, stop_loss))

        return signals

    def _calculate_stop_loss(self, side, price):
        """
        计算止损价格

        参数:
            side: str, 交易方向 ('buy' 或 'sell')
            price: float, 交易价格

        返回:
            float: 止损价格
        """
        # 使用ATR计算止损距离
        if 'atr' in self.data.columns:
            atr = self.data['atr'].iloc[-1]
        else:
            # 如果没有ATR，使用价格的一定百分比
            atr = price * 0.01

        # 根据市场环境调整ATR倍数
        if self.market_env == "trend":
            multiplier = 3.0  # 趋势市场使用更宽松的止损
        elif self.market_env == "range":
            multiplier = 2.0  # 震荡市场使用适中的止损
        else:  # volatile
            multiplier = 2.5  # 多变市场使用较宽松的止损

        if side == 'buy':
            stop_loss = price - (atr * multiplier)
        else:  # sell
            stop_loss = price + (atr * multiplier)

        return stop_loss

    def calculate_position_size(self, account_balance, risk_per_trade, entry_price, stop_loss):
        """
        计算仓位大小

        参数:
            account_balance: float, 账户余额
            risk_per_trade: float, 每笔交易风险比例 (0-1)
            entry_price: float, 入场价格
            stop_loss: float, 止损价格

        返回:
            float: 建议的仓位大小
        """
        # 计算每笔交易的风险金额
        risk_amount = account_balance * risk_per_trade

        # 计算止损距离
        stop_distance = abs(entry_price - stop_loss)

        # 避免除零错误
        if stop_distance == 0:
            stop_distance = entry_price * 0.01  # 使用1%作为默认止损距离

        # 计算仓位大小
        position_size = risk_amount / stop_distance

        return position_size

    def get_market_status(self):
        """
        获取市场状态信息

        返回:
            dict: 市场状态信息
        """
        if self.data is None or len(self.data) < 1:
            return {
                'market_env': 'unknown',
                'recommendation': 'HOLD',
                'signals': []
            }

        # 获取最新数据
        current = self.data.iloc[-1]

        # 生成交易信号
        signals = self.generate_signals()

        # 生成建议
        if signals:
            if signals[0][0] == 'buy':
                recommendation = 'BUY'
            elif signals[0][0] == 'sell':
                recommendation = 'SELL'
            else:
                recommendation = 'HOLD'
        else:
            recommendation = 'HOLD'

        # 收集指标数据
        indicators = {}
        for indicator in ['trend_line', 'rsi', 'macd', 'sma_fast', 'sma_slow', 'bb_width', 'atr']:
            if indicator in current:
                indicators[indicator] = current[indicator]

        # 返回市场状态
        return {
            'market_env': self.market_env,
            'recommendation': recommendation,
            'signals': signals,
            'indicators': indicators
        }

# 多周期分析函数
def analyze_multi_timeframe(results, weights=None):
    """
    多周期加权分析

    参数:
        results: dict, 不同时间周期的分析结果
        weights: dict, 不同时间周期的权重

    返回:
        str: 综合建议 ('BUY', 'SELL', 或 'HOLD')
    """
    if not results:
        return 'HOLD'

    # 默认权重
    if weights is None:
        weights = {
            '3m': 0.2,    # 3分钟周期权重较低
            '15m': 0.3,   # 15分钟周期权重中等
            '30m': 0.5    # 30分钟周期权重较高
        }

    buy_score = 0
    sell_score = 0

    for interval, result in results.items():
        if 'recommendation' in result:
            if result['recommendation'] == 'BUY':
                buy_score += weights.get(interval, 0.3)
            elif result['recommendation'] == 'SELL':
                sell_score += weights.get(interval, 0.3)

    # 设置决策阈值
    threshold = 0.4

    if buy_score > threshold and buy_score > sell_score:
        return 'BUY'
    elif sell_score > threshold and sell_score > buy_score:
        return 'SELL'
    else:
        return 'HOLD'
